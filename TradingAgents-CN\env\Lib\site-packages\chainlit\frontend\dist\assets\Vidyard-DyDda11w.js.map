{"version": 3, "file": "Vidyard-DyDda11w.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Vidyard.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Vidyard_exports = {};\n__export(Vidyard_exports, {\n  default: () => Vidyard\n});\nmodule.exports = __toCommonJS(Vidyard_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://play.vidyard.com/embed/v4.js\";\nconst SDK_GLOBAL = \"VidyardV4\";\nconst SDK_GLOBAL_READY = \"onVidyardAPI\";\nclass Vidyard extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, config, onError, onDuration } = this.props;\n    const id = url && url.match(import_patterns.MATCH_URL_VIDYARD)[1];\n    if (this.player) {\n      this.stop();\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((Vidyard2) => {\n      if (!this.container)\n        return;\n      Vidyard2.api.addReadyListener((data, player) => {\n        if (this.player) {\n          return;\n        }\n        this.player = player;\n        this.player.on(\"ready\", this.props.onReady);\n        this.player.on(\"play\", this.props.onPlay);\n        this.player.on(\"pause\", this.props.onPause);\n        this.player.on(\"seek\", this.props.onSeek);\n        this.player.on(\"playerComplete\", this.props.onEnded);\n      }, id);\n      Vidyard2.api.renderPlayer({\n        uuid: id,\n        container: this.container,\n        autoplay: playing ? 1 : 0,\n        ...config.options\n      });\n      Vidyard2.api.getPlayerMetadata(id).then((meta) => {\n        this.duration = meta.length_in_seconds;\n        onDuration(meta.length_in_seconds);\n      });\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    window.VidyardV4.api.destroyPlayer(this.player);\n  }\n  seekTo(amount, keepPlaying = true) {\n    this.callPlayer(\"seek\", amount);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackSpeed\", rate);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"currentTime\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(Vidyard, \"displayName\", \"Vidyard\");\n__publicField(Vidyard, \"canPlay\", import_patterns.canPlay.vidyard);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Vidyard_exports", "<PERSON><PERSON><PERSON>", "Vidyard_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "container", "url", "playing", "config", "onError", "onDuration", "id", "Vidyard2", "data", "player", "meta", "amount", "keepPlaying", "fraction", "rate", "display", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAkB,CAAE,EACxBd,EAASc,EAAiB,CACxB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAe,EACzCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,uCACVC,EAAa,YACbC,EAAmB,eACzB,MAAMV,UAAgBE,EAAa,SAAU,CAC3C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,UAAU,CAAC,CACtB,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAC9B,KAAK,MAAM,SAAW,MACxB,KAAK,UAAU,KAAK,MAAM,MAAM,CAExC,CAAK,EACDA,EAAc,KAAM,MAAQa,GAAc,CACxC,KAAK,UAAYA,CACvB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKC,EAAK,CACR,KAAM,CAAE,QAAAC,EAAS,OAAAC,EAAQ,QAAAC,EAAS,WAAAC,CAAU,EAAK,KAAK,MAChDC,EAAKL,GAAOA,EAAI,MAAMN,EAAgB,iBAAiB,EAAE,CAAC,EAC5D,KAAK,QACP,KAAK,KAAM,KAETF,EAAa,QAAQI,EAASC,EAAYC,CAAgB,EAAE,KAAMQ,GAAa,CAC5E,KAAK,YAEVA,EAAS,IAAI,iBAAiB,CAACC,EAAMC,IAAW,CAC1C,KAAK,SAGT,KAAK,OAASA,EACd,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,OAAQ,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,OAAQ,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,GAAG,iBAAkB,KAAK,MAAM,OAAO,EACpD,EAAEH,CAAE,EACLC,EAAS,IAAI,aAAa,CACxB,KAAMD,EACN,UAAW,KAAK,UAChB,SAAUJ,EAAU,EAAI,EACxB,GAAGC,EAAO,OAClB,CAAO,EACDI,EAAS,IAAI,kBAAkBD,CAAE,EAAE,KAAMI,GAAS,CAChD,KAAK,SAAWA,EAAK,kBACrBL,EAAWK,EAAK,iBAAiB,CACzC,CAAO,EACF,EAAEN,CAAO,CACd,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACL,OAAO,UAAU,IAAI,cAAc,KAAK,MAAM,CAClD,CACE,OAAOO,EAAQC,EAAc,GAAM,CACjC,KAAK,WAAW,OAAQD,CAAM,EACzBC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,CAAQ,CACzC,CACE,gBAAgBC,EAAM,CACpB,KAAK,WAAW,mBAAoBA,CAAI,CAC5C,CACE,aAAc,CACZ,OAAO,KAAK,QAChB,CACE,gBAAiB,CACf,OAAO,KAAK,WAAW,aAAa,CACxC,CACE,kBAAmB,CACjB,OAAO,IACX,CACE,QAAS,CACP,KAAM,CAAE,QAAAC,GAAY,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACD,EACD,OAAuBxB,EAAa,QAAQ,cAAc,MAAO,CAAE,MAAAyB,CAAK,EAAoBzB,EAAa,QAAQ,cAAc,MAAO,CAAE,IAAK,KAAK,GAAG,CAAE,CAAC,CAC5J,CACA,CACAJ,EAAcE,EAAS,cAAe,SAAS,EAC/CF,EAAcE,EAAS,UAAWM,EAAgB,QAAQ,OAAO", "x_google_ignoreList": [0]}