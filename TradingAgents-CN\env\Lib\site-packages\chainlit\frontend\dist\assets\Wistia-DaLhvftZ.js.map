{"version": 3, "file": "Wistia-DaLhvftZ.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Wistia.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Wistia_exports = {};\n__export(Wistia_exports, {\n  default: () => Wistia\n});\nmodule.exports = __toCommonJS(Wistia_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://fast.wistia.com/assets/external/E-v1.js\";\nconst SDK_GLOBAL = \"Wistia\";\nconst PLAYER_ID_PREFIX = \"wistia-player-\";\nclass Wistia extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onSeek\", (...args) => this.props.onSeek(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onPlaybackRateChange\", (...args) => this.props.onPlaybackRateChange(...args));\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { playing, muted, controls, onReady, config, onError } = this.props;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Wistia2) => {\n      if (config.customControls) {\n        config.customControls.forEach((control) => Wistia2.defineControl(control));\n      }\n      window._wq = window._wq || [];\n      window._wq.push({\n        id: this.playerID,\n        options: {\n          autoPlay: playing,\n          silentAutoPlay: \"allow\",\n          muted,\n          controlsVisibleOnLoad: controls,\n          fullscreenButton: controls,\n          playbar: controls,\n          playbackRateControl: controls,\n          qualityControl: controls,\n          volumeControl: controls,\n          settingsControl: controls,\n          smallPlayButton: controls,\n          ...config.options\n        },\n        onReady: (player) => {\n          this.player = player;\n          this.unbind();\n          this.player.bind(\"play\", this.onPlay);\n          this.player.bind(\"pause\", this.onPause);\n          this.player.bind(\"seek\", this.onSeek);\n          this.player.bind(\"end\", this.onEnded);\n          this.player.bind(\"playbackratechange\", this.onPlaybackRateChange);\n          onReady();\n        }\n      });\n    }, onError);\n  }\n  unbind() {\n    this.player.unbind(\"play\", this.onPlay);\n    this.player.unbind(\"pause\", this.onPause);\n    this.player.unbind(\"seek\", this.onSeek);\n    this.player.unbind(\"end\", this.onEnded);\n    this.player.unbind(\"playbackratechange\", this.onPlaybackRateChange);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.unbind();\n    this.callPlayer(\"remove\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"time\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"volume\", fraction);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"playbackRate\", rate);\n  }\n  getDuration() {\n    return this.callPlayer(\"duration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"time\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { url } = this.props;\n    const videoID = url && url.match(import_patterns.MATCH_URL_WISTIA)[1];\n    const className = `wistia_embed wistia_async_${videoID}`;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { id: this.playerID, key: videoID, className, style });\n  }\n}\n__publicField(Wistia, \"displayName\", \"Wistia\");\n__publicField(Wistia, \"canPlay\", import_patterns.canPlay.wistia);\n__publicField(Wistia, \"loopOnEnded\", true);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Wistia_exports", "Wistia", "Wistia_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "PLAYER_ID_PREFIX", "args", "url", "playing", "muted", "controls", "onReady", "config", "onError", "Wistia2", "control", "player", "seconds", "keepPlaying", "fraction", "rate", "videoID", "className", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAiB,CAAE,EACvBd,EAASc,EAAgB,CACvB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAc,EACxCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,kDACVC,EAAa,SACbC,EAAmB,iBACzB,MAAMV,UAAeE,EAAa,SAAU,CAC1C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,WAAY,KAAK,MAAM,OAAO,UAAY,GAAGY,CAAgB,MAAON,EAAa,cAAY,CAAG,EAAE,EAEtHN,EAAc,KAAM,SAAU,IAAIa,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,CAAC,EACrEb,EAAc,KAAM,UAAW,IAAIa,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvEb,EAAc,KAAM,SAAU,IAAIa,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,CAAC,EACrEb,EAAc,KAAM,UAAW,IAAIa,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvEb,EAAc,KAAM,uBAAwB,IAAIa,IAAS,KAAK,MAAM,qBAAqB,GAAGA,CAAI,CAAC,EACjGb,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,WAAW,MAAM,CAC5B,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,WAAW,QAAQ,CAC9B,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKc,EAAK,CACR,KAAM,CAAE,QAAAC,EAAS,MAAAC,EAAO,SAAAC,EAAU,QAAAC,EAAS,OAAAC,EAAQ,QAAAC,GAAY,KAAK,SAChEd,EAAa,QAAQI,EAASC,CAAU,EAAE,KAAMU,GAAY,CAC1DF,EAAO,gBACTA,EAAO,eAAe,QAASG,GAAYD,EAAQ,cAAcC,CAAO,CAAC,EAE3E,OAAO,IAAM,OAAO,KAAO,CAAE,EAC7B,OAAO,IAAI,KAAK,CACd,GAAI,KAAK,SACT,QAAS,CACP,SAAUP,EACV,eAAgB,QAChB,MAAAC,EACA,sBAAuBC,EACvB,iBAAkBA,EAClB,QAASA,EACT,oBAAqBA,EACrB,eAAgBA,EAChB,cAAeA,EACf,gBAAiBA,EACjB,gBAAiBA,EACjB,GAAGE,EAAO,OACX,EACD,QAAUI,GAAW,CACnB,KAAK,OAASA,EACd,KAAK,OAAQ,EACb,KAAK,OAAO,KAAK,OAAQ,KAAK,MAAM,EACpC,KAAK,OAAO,KAAK,QAAS,KAAK,OAAO,EACtC,KAAK,OAAO,KAAK,OAAQ,KAAK,MAAM,EACpC,KAAK,OAAO,KAAK,MAAO,KAAK,OAAO,EACpC,KAAK,OAAO,KAAK,qBAAsB,KAAK,oBAAoB,EAChEL,EAAS,CACnB,CACA,CAAO,CACF,EAAEE,CAAO,CACd,CACE,QAAS,CACP,KAAK,OAAO,OAAO,OAAQ,KAAK,MAAM,EACtC,KAAK,OAAO,OAAO,QAAS,KAAK,OAAO,EACxC,KAAK,OAAO,OAAO,OAAQ,KAAK,MAAM,EACtC,KAAK,OAAO,OAAO,MAAO,KAAK,OAAO,EACtC,KAAK,OAAO,OAAO,qBAAsB,KAAK,oBAAoB,CACtE,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACL,KAAK,OAAQ,EACb,KAAK,WAAW,QAAQ,CAC5B,CACE,OAAOI,EAASC,EAAc,GAAM,CAClC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,SAAUA,CAAQ,CACtC,CACE,gBAAgBC,EAAM,CACpB,KAAK,WAAW,eAAgBA,CAAI,CACxC,CACE,aAAc,CACZ,OAAO,KAAK,WAAW,UAAU,CACrC,CACE,gBAAiB,CACf,OAAO,KAAK,WAAW,MAAM,CACjC,CACE,kBAAmB,CACjB,OAAO,IACX,CACE,QAAS,CACP,KAAM,CAAE,IAAAb,GAAQ,KAAK,MACfc,EAAUd,GAAOA,EAAI,MAAMN,EAAgB,gBAAgB,EAAE,CAAC,EAC9DqB,EAAY,6BAA6BD,CAAO,GAChDE,EAAQ,CACZ,MAAO,OACP,OAAQ,MACT,EACD,OAAuB1B,EAAa,QAAQ,cAAc,MAAO,CAAE,GAAI,KAAK,SAAU,IAAKwB,EAAS,UAAAC,EAAW,MAAAC,CAAK,CAAE,CAC1H,CACA,CACA9B,EAAcE,EAAQ,cAAe,QAAQ,EAC7CF,EAAcE,EAAQ,UAAWM,EAAgB,QAAQ,MAAM,EAC/DR,EAAcE,EAAQ,cAAe,EAAI", "x_google_ignoreList": [0]}