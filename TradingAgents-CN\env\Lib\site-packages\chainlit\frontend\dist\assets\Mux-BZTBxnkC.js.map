{"version": 3, "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,GAAI,aAAc,CAAE,MAAO,EAAM,GAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAc,CAAE,EACpBd,EAASc,EAAa,CACpB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAW,EACrCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAkBC,EACtB,MAAMC,EAAU,2EAChB,MAAMN,UAAYE,EAAa,SAAU,CACvC,aAAc,CACZ,MAAM,GAAG,SAAS,EAElBJ,EAAc,KAAM,UAAW,IAAIS,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvET,EAAc,KAAM,SAAU,IAAIS,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,CAAC,EACrET,EAAc,KAAM,WAAY,IAAIS,IAAS,KAAK,MAAM,SAAS,GAAGA,CAAI,CAAC,EACzET,EAAc,KAAM,cAAe,IAAIS,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,CAAC,EAC/ET,EAAc,KAAM,UAAW,IAAIS,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvET,EAAc,KAAM,UAAW,IAAIS,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvET,EAAc,KAAM,UAAW,IAAIS,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvET,EAAc,KAAM,uBAAyBU,GAAU,KAAK,MAAM,qBAAqBA,EAAM,OAAO,YAAY,CAAC,EACjHV,EAAc,KAAM,cAAe,IAAIS,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,CAAC,EAC/ET,EAAc,KAAM,SAAW,GAAM,CACnC,KAAK,MAAM,OAAO,EAAE,OAAO,WAAW,CAC5C,CAAK,EACDA,EAAc,KAAM,mBAAoB,IAAM,CAC5C,MAAMW,EAAW,KAAK,YAAa,EACnC,KAAK,MAAM,WAAWA,CAAQ,CACpC,CAAK,EACDX,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,OAAO,MAAQ,EAC1B,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,OAAO,MAAQ,EAC1B,CAAK,EACDA,EAAc,KAAM,MAAQY,GAAW,CACrC,KAAK,OAASA,CACpB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,EAC7C,KAAK,aAAa,KAAK,MAAM,EAC7B,MAAMC,EAAa,KAAK,cAAc,KAAK,MAAM,GAAG,EAChDA,IACF,KAAK,OAAO,WAAaA,EAE/B,CACE,sBAAuB,CACrB,KAAK,OAAO,WAAa,KACzB,KAAK,gBAAgB,KAAK,MAAM,CACpC,CACE,aAAaD,EAAQ,CACnB,KAAM,CAAE,YAAAE,GAAgB,KAAK,MAC7BF,EAAO,iBAAiB,OAAQ,KAAK,MAAM,EAC3CA,EAAO,iBAAiB,UAAW,KAAK,QAAQ,EAChDA,EAAO,iBAAiB,UAAW,KAAK,WAAW,EACnDA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,SAAU,KAAK,MAAM,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,aAAc,KAAK,oBAAoB,EAC/DA,EAAO,iBAAiB,wBAAyB,KAAK,WAAW,EACjEA,EAAO,iBAAiB,wBAAyB,KAAK,YAAY,EAClEA,EAAO,iBAAiB,gCAAiC,KAAK,wBAAwB,EACtFA,EAAO,iBAAiB,UAAW,KAAK,OAAO,EAC3CE,GACFF,EAAO,aAAa,cAAe,EAAE,CAE3C,CACE,gBAAgBA,EAAQ,CACtBA,EAAO,oBAAoB,UAAW,KAAK,OAAO,EAClDA,EAAO,oBAAoB,OAAQ,KAAK,MAAM,EAC9CA,EAAO,oBAAoB,UAAW,KAAK,QAAQ,EACnDA,EAAO,oBAAoB,UAAW,KAAK,WAAW,EACtDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,SAAU,KAAK,MAAM,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,aAAc,KAAK,oBAAoB,EAClEA,EAAO,oBAAoB,wBAAyB,KAAK,WAAW,EACpEA,EAAO,oBAAoB,wBAAyB,KAAK,YAAY,EACrEA,EAAO,oBAAoB,UAAW,KAAK,OAAO,CACtD,CACE,MAAM,KAAKG,EAAK,CACd,IAAIC,EACJ,KAAM,CAAE,QAAAC,EAAS,OAAAC,CAAQ,EAAG,KAAK,MACjC,GAAI,GAAGF,EAAK,WAAW,iBAAmB,MAAgBA,EAAG,IAAI,YAAY,GAC3E,GAAI,CACF,MAAMG,EAASX,EAAQ,QAAQ,UAAWU,EAAO,OAAO,EACxD,MAAME,EAAA,WAEJ,GAAGD,CAAM,QAEX,KAAK,MAAM,SAAU,CACtB,OAAQE,EAAO,CACdJ,EAAQI,CAAK,CACrB,CAEI,KAAM,EAAGC,CAAE,EAAIP,EAAI,MAAMT,EAAgB,aAAa,EACtD,KAAK,OAAO,WAAagB,CAC7B,CACE,MAAO,CACL,MAAMC,EAAU,KAAK,OAAO,KAAM,EAC9BA,GACFA,EAAQ,MAAM,KAAK,MAAM,OAAO,CAEtC,CACE,OAAQ,CACN,KAAK,OAAO,MAAO,CACvB,CACE,MAAO,CACL,KAAK,OAAO,WAAa,IAC7B,CACE,OAAOC,EAASC,EAAc,GAAM,CAClC,KAAK,OAAO,YAAcD,EACrBC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,OAAO,OAASA,CACzB,CACE,WAAY,CACN,KAAK,OAAO,yBAA2B,SAAS,0BAA4B,KAAK,QACnF,KAAK,OAAO,wBAAyB,CAE3C,CACE,YAAa,CACP,SAAS,sBAAwB,SAAS,0BAA4B,KAAK,QAC7E,SAAS,qBAAsB,CAErC,CACE,gBAAgBC,EAAM,CACpB,GAAI,CACF,KAAK,OAAO,aAAeA,CAC5B,OAAQN,EAAO,CACd,KAAK,MAAM,QAAQA,CAAK,CAC9B,CACA,CACE,aAAc,CACZ,GAAI,CAAC,KAAK,OACR,OAAO,KACT,KAAM,CAAE,SAAAV,EAAU,SAAAiB,CAAU,EAAG,KAAK,OACpC,OAAIjB,IAAa,KAAYiB,EAAS,OAAS,EACtCA,EAAS,IAAIA,EAAS,OAAS,CAAC,EAElCjB,CACX,CACE,gBAAiB,CACf,OAAK,KAAK,OAEH,KAAK,OAAO,YADV,IAEb,CACE,kBAAmB,CACjB,GAAI,CAAC,KAAK,OACR,OAAO,KACT,KAAM,CAAE,SAAAkB,GAAa,KAAK,OAC1B,GAAIA,EAAS,SAAW,EACtB,MAAO,GAET,MAAMC,EAAMD,EAAS,IAAIA,EAAS,OAAS,CAAC,EACtClB,EAAW,KAAK,YAAa,EACnC,OAAImB,EAAMnB,EACDA,EAEFmB,CACX,CACE,cAAcf,EAAK,CACjB,KAAM,EAAGO,CAAE,EAAIP,EAAI,MAAMT,EAAgB,aAAa,EACtD,OAAOgB,CACX,CACE,QAAS,CACP,KAAM,CAAE,IAAAP,EAAK,QAAAgB,EAAS,KAAAC,EAAM,SAAAC,EAAU,MAAAC,EAAO,OAAAhB,EAAQ,MAAAiB,EAAO,OAAAC,CAAQ,EAAG,KAAK,MACtEC,EAAQ,CACZ,MAAOF,IAAU,OAASA,EAAQ,OAClC,OAAQC,IAAW,OAASA,EAAS,MACtC,EACD,OAAIH,IAAa,KACfI,EAAM,YAAY,EAAI,QAEDjC,EAAa,QAAQ,cAC1C,aACA,CACE,IAAK,KAAK,IACV,cAAe,KAAK,cAAcW,CAAG,EACrC,MAAAsB,EACA,QAAS,OACT,SAAUN,GAAW,OACrB,MAAOG,EAAQ,GAAK,OACpB,KAAMF,EAAO,GAAK,OAClB,GAAGd,EAAO,UAClB,CACK,CACL,CACA,CACAlB,EAAcE,EAAK,cAAe,KAAK,EACvCF,EAAcE,EAAK,UAAWI,EAAgB,QAAQ,GAAG", "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Mux_exports", "<PERSON><PERSON>", "Mux_1", "import_react", "require$$0", "import_patterns", "require$$1", "SDK_URL", "args", "event", "duration", "player", "playbackId", "playsinline", "url", "_a", "onError", "config", "sdkUrl", "__vitePreload", "error", "id", "promise", "seconds", "keepPlaying", "fraction", "rate", "seekable", "buffered", "end", "playing", "loop", "controls", "muted", "width", "height", "style"], "ignoreList": [0], "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Mux.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Mux_exports = {};\n__export(Mux_exports, {\n  default: () => Mux\n});\nmodule.exports = __toCommonJS(Mux_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://cdn.jsdelivr.net/npm/@mux/mux-player@VERSION/dist/mux-player.mjs\";\nclass Mux extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"ref\", (player) => {\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const playbackId = this.getPlaybackId(this.props.url);\n    if (playbackId) {\n      this.player.playbackId = playbackId;\n    }\n  }\n  componentWillUnmount() {\n    this.player.playbackId = null;\n    this.removeListeners(this.player);\n  }\n  addListeners(player) {\n    const { playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    player.addEventListener(\"canplay\", this.onReady);\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n    }\n  }\n  removeListeners(player) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"canplay\", this.onReady);\n  }\n  async load(url) {\n    var _a;\n    const { onError, config } = this.props;\n    if (!((_a = globalThis.customElements) == null ? void 0 : _a.get(\"mux-player\"))) {\n      try {\n        const sdkUrl = SDK_URL.replace(\"VERSION\", config.version);\n        await import(\n          /* webpackIgnore: true */\n          `${sdkUrl}`\n        );\n        this.props.onLoaded();\n      } catch (error) {\n        onError(error);\n      }\n    }\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    this.player.playbackId = id;\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.playbackId = null;\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getPlaybackId(url) {\n    const [, id] = url.match(import_patterns.MATCH_URL_MUX);\n    return id;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    if (controls === false) {\n      style[\"--controls\"] = \"none\";\n    }\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"mux-player\",\n      {\n        ref: this.ref,\n        \"playback-id\": this.getPlaybackId(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        muted: muted ? \"\" : void 0,\n        loop: loop ? \"\" : void 0,\n        ...config.attributes\n      }\n    );\n  }\n}\n__publicField(Mux, \"displayName\", \"Mux\");\n__publicField(Mux, \"canPlay\", import_patterns.canPlay.mux);\n"], "file": "assets/Mux-BZTBxnkC.js"}