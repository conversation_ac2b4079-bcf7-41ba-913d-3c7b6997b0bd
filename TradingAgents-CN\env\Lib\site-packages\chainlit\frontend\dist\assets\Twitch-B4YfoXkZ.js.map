{"version": 3, "file": "Twitch-B4YfoXkZ.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Twitch.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Twitch_exports = {};\n__export(Twitch_exports, {\n  default: () => Twitch\n});\nmodule.exports = __toCommonJS(Twitch_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://player.twitch.tv/js/embed/v1.js\";\nconst SDK_GLOBAL = \"Twitch\";\nconst PLAYER_ID_PREFIX = \"twitch-player-\";\nclass Twitch extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"setMuted\", true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"setMuted\", false);\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    const { playsinline, onError, config, controls } = this.props;\n    const isChannel = import_patterns.MATCH_URL_TWITCH_CHANNEL.test(url);\n    const id = isChannel ? url.match(import_patterns.MATCH_URL_TWITCH_CHANNEL)[1] : url.match(import_patterns.MATCH_URL_TWITCH_VIDEO)[1];\n    if (isReady) {\n      if (isChannel) {\n        this.player.setChannel(id);\n      } else {\n        this.player.setVideo(\"v\" + id);\n      }\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Twitch2) => {\n      this.player = new Twitch2.Player(this.playerID, {\n        video: isChannel ? \"\" : id,\n        channel: isChannel ? id : \"\",\n        height: \"100%\",\n        width: \"100%\",\n        playsinline,\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        // https://github.com/CookPete/react-player/issues/733#issuecomment-549085859\n        controls: isChannel ? true : controls,\n        time: (0, import_utils.parseStartTime)(url),\n        ...config.options\n      });\n      const { READY, PLAYING, PAUSE, ENDED, ONLINE, OFFLINE, SEEK } = Twitch2.Player;\n      this.player.addEventListener(READY, this.props.onReady);\n      this.player.addEventListener(PLAYING, this.props.onPlay);\n      this.player.addEventListener(PAUSE, this.props.onPause);\n      this.player.addEventListener(ENDED, this.props.onEnded);\n      this.player.addEventListener(SEEK, this.props.onSeek);\n      this.player.addEventListener(ONLINE, this.props.onLoaded);\n      this.player.addEventListener(OFFLINE, this.props.onLoaded);\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.callPlayer(\"pause\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentTime\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style, id: this.playerID });\n  }\n}\n__publicField(Twitch, \"displayName\", \"Twitch\");\n__publicField(Twitch, \"canPlay\", import_patterns.canPlay.twitch);\n__publicField(Twitch, \"loopOnEnded\", true);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Twitch_exports", "Twitch", "Twitch_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "PLAYER_ID_PREFIX", "url", "isReady", "playsinline", "onError", "config", "controls", "isChannel", "id", "Twitch2", "READY", "PLAYING", "PAUSE", "ENDED", "ONLINE", "OFFLINE", "SEEK", "seconds", "keepPlaying", "fraction", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAiB,CAAE,EACvBd,EAASc,EAAgB,CACvB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAc,EACxCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,0CACVC,EAAa,SACbC,EAAmB,iBACzB,MAAMV,UAAeE,EAAa,SAAU,CAC1C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,WAAY,KAAK,MAAM,OAAO,UAAY,GAAGY,CAAgB,MAAON,EAAa,cAAY,CAAG,EAAE,EACtHN,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,WAAW,WAAY,EAAI,CACtC,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,WAAW,WAAY,EAAK,CACvC,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKa,EAAKC,EAAS,CACjB,KAAM,CAAE,YAAAC,EAAa,QAAAC,EAAS,OAAAC,EAAQ,SAAAC,CAAQ,EAAK,KAAK,MAClDC,EAAYX,EAAgB,yBAAyB,KAAKK,CAAG,EAC7DO,EAAKD,EAAYN,EAAI,MAAML,EAAgB,wBAAwB,EAAE,CAAC,EAAIK,EAAI,MAAML,EAAgB,sBAAsB,EAAE,CAAC,EACnI,GAAIM,EAAS,CACPK,EACF,KAAK,OAAO,WAAWC,CAAE,EAEzB,KAAK,OAAO,SAAS,IAAMA,CAAE,EAE/B,MACN,IACQd,EAAa,QAAQI,EAASC,CAAU,EAAE,KAAMU,GAAY,CAC9D,KAAK,OAAS,IAAIA,EAAQ,OAAO,KAAK,SAAU,CAC9C,MAAOF,EAAY,GAAKC,EACxB,QAASD,EAAYC,EAAK,GAC1B,OAAQ,OACR,MAAO,OACP,YAAAL,EACA,SAAU,KAAK,MAAM,QACrB,MAAO,KAAK,MAAM,MAElB,SAAUI,EAAY,GAAOD,EAC7B,QAAUZ,EAAa,gBAAgBO,CAAG,EAC1C,GAAGI,EAAO,OAClB,CAAO,EACD,KAAM,CAAE,MAAAK,EAAO,QAAAC,EAAS,MAAAC,EAAO,MAAAC,EAAO,OAAAC,EAAQ,QAAAC,EAAS,KAAAC,GAASP,EAAQ,OACxE,KAAK,OAAO,iBAAiBC,EAAO,KAAK,MAAM,OAAO,EACtD,KAAK,OAAO,iBAAiBC,EAAS,KAAK,MAAM,MAAM,EACvD,KAAK,OAAO,iBAAiBC,EAAO,KAAK,MAAM,OAAO,EACtD,KAAK,OAAO,iBAAiBC,EAAO,KAAK,MAAM,OAAO,EACtD,KAAK,OAAO,iBAAiBG,EAAM,KAAK,MAAM,MAAM,EACpD,KAAK,OAAO,iBAAiBF,EAAQ,KAAK,MAAM,QAAQ,EACxD,KAAK,OAAO,iBAAiBC,EAAS,KAAK,MAAM,QAAQ,CAC1D,EAAEX,CAAO,CACd,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACL,KAAK,WAAW,OAAO,CAC3B,CACE,OAAOa,EAASC,EAAc,GAAM,CAClC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,CAAQ,CACzC,CACE,aAAc,CACZ,OAAO,KAAK,WAAW,aAAa,CACxC,CACE,gBAAiB,CACf,OAAO,KAAK,WAAW,gBAAgB,CAC3C,CACE,kBAAmB,CACjB,OAAO,IACX,CACE,QAAS,CACP,MAAMC,EAAQ,CACZ,MAAO,OACP,OAAQ,MACT,EACD,OAAuB5B,EAAa,QAAQ,cAAc,MAAO,CAAE,MAAA4B,EAAO,GAAI,KAAK,SAAU,CACjG,CACA,CACAhC,EAAcE,EAAQ,cAAe,QAAQ,EAC7CF,EAAcE,EAAQ,UAAWM,EAAgB,QAAQ,MAAM,EAC/DR,EAAcE,EAAQ,cAAe,EAAI", "x_google_ignoreList": [0]}