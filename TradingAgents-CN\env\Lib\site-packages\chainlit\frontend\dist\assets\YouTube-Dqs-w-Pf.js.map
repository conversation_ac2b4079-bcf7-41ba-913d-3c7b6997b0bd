{"version": 3, "file": "YouTube-Dqs-w-Pf.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/YouTube.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar YouTube_exports = {};\n__export(YouTube_exports, {\n  default: () => YouTube\n});\nmodule.exports = __toCommonJS(YouTube_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://www.youtube.com/iframe_api\";\nconst SDK_GLOBAL = \"YT\";\nconst SDK_GLOBAL_READY = \"onYouTubeIframeAPIReady\";\nconst MATCH_PLAYLIST = /[?&](?:list|channel)=([a-zA-Z0-9_-]+)/;\nconst MATCH_USER_UPLOADS = /user\\/([a-zA-Z0-9_-]+)\\/?/;\nconst MATCH_NOCOOKIE = /youtube-nocookie\\.com/;\nconst NOCOOKIE_HOST = \"https://www.youtube-nocookie.com\";\nclass YouTube extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"parsePlaylist\", (url) => {\n      if (url instanceof Array) {\n        return {\n          listType: \"playlist\",\n          playlist: url.map(this.getID).join(\",\")\n        };\n      }\n      if (MATCH_PLAYLIST.test(url)) {\n        const [, playlistId] = url.match(MATCH_PLAYLIST);\n        return {\n          listType: \"playlist\",\n          list: playlistId.replace(/^UC/, \"UU\")\n        };\n      }\n      if (MATCH_USER_UPLOADS.test(url)) {\n        const [, username] = url.match(MATCH_USER_UPLOADS);\n        return {\n          listType: \"user_uploads\",\n          list: username\n        };\n      }\n      return {};\n    });\n    __publicField(this, \"onStateChange\", (event) => {\n      const { data } = event;\n      const { onPlay, onPause, onBuffer, onBufferEnd, onEnded, onReady, loop, config: { playerVars, onUnstarted } } = this.props;\n      const { UNSTARTED, PLAYING, PAUSED, BUFFERING, ENDED, CUED } = window[SDK_GLOBAL].PlayerState;\n      if (data === UNSTARTED)\n        onUnstarted();\n      if (data === PLAYING) {\n        onPlay();\n        onBufferEnd();\n      }\n      if (data === PAUSED)\n        onPause();\n      if (data === BUFFERING)\n        onBuffer();\n      if (data === ENDED) {\n        const isPlaylist = !!this.callPlayer(\"getPlaylist\");\n        if (loop && !isPlaylist) {\n          if (playerVars.start) {\n            this.seekTo(playerVars.start);\n          } else {\n            this.play();\n          }\n        }\n        onEnded();\n      }\n      if (data === CUED)\n        onReady();\n    });\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unMute\");\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  getID(url) {\n    if (!url || url instanceof Array || MATCH_PLAYLIST.test(url)) {\n      return null;\n    }\n    return url.match(import_patterns.MATCH_URL_YOUTUBE)[1];\n  }\n  load(url, isReady) {\n    const { playing, muted, playsinline, controls, loop, config, onError } = this.props;\n    const { playerVars, embedOptions } = config;\n    const id = this.getID(url);\n    if (isReady) {\n      if (MATCH_PLAYLIST.test(url) || MATCH_USER_UPLOADS.test(url) || url instanceof Array) {\n        this.player.loadPlaylist(this.parsePlaylist(url));\n        return;\n      }\n      this.player.cueVideoById({\n        videoId: id,\n        startSeconds: (0, import_utils.parseStartTime)(url) || playerVars.start,\n        endSeconds: (0, import_utils.parseEndTime)(url) || playerVars.end\n      });\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, (YT) => YT.loaded).then((YT) => {\n      if (!this.container)\n        return;\n      this.player = new YT.Player(this.container, {\n        width: \"100%\",\n        height: \"100%\",\n        videoId: id,\n        playerVars: {\n          autoplay: playing ? 1 : 0,\n          mute: muted ? 1 : 0,\n          controls: controls ? 1 : 0,\n          start: (0, import_utils.parseStartTime)(url),\n          end: (0, import_utils.parseEndTime)(url),\n          origin: window.location.origin,\n          playsinline: playsinline ? 1 : 0,\n          ...this.parsePlaylist(url),\n          ...playerVars\n        },\n        events: {\n          onReady: () => {\n            if (loop) {\n              this.player.setLoop(true);\n            }\n            this.props.onReady();\n          },\n          onPlaybackRateChange: (event) => this.props.onPlaybackRateChange(event.data),\n          onPlaybackQualityChange: (event) => this.props.onPlaybackQualityChange(event),\n          onStateChange: this.onStateChange,\n          onError: (event) => onError(event.data)\n        },\n        host: MATCH_NOCOOKIE.test(url) ? NOCOOKIE_HOST : void 0,\n        ...embedOptions\n      });\n    }, onError);\n    if (embedOptions.events) {\n      console.warn(\"Using `embedOptions.events` will likely break things. Use ReactPlayer\\u2019s callback props instead, eg onReady, onPlay, onPause\");\n    }\n  }\n  play() {\n    this.callPlayer(\"playVideo\");\n  }\n  pause() {\n    this.callPlayer(\"pauseVideo\");\n  }\n  stop() {\n    if (!document.body.contains(this.callPlayer(\"getIframe\")))\n      return;\n    this.callPlayer(\"stopVideo\");\n  }\n  seekTo(amount, keepPlaying = false) {\n    this.callPlayer(\"seekTo\", amount);\n    if (!keepPlaying && !this.props.playing) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackRate\", rate);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentTime\");\n  }\n  getSecondsLoaded() {\n    return this.callPlayer(\"getVideoLoadedFraction\") * this.getDuration();\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(YouTube, \"displayName\", \"YouTube\");\n__publicField(YouTube, \"canPlay\", import_patterns.canPlay.youtube);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "YouTube_exports", "YouTube", "YouTube_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "MATCH_PLAYLIST", "MATCH_USER_UPLOADS", "MATCH_NOCOOKIE", "NOCOOKIE_HOST", "url", "playlistId", "username", "event", "data", "onPlay", "onPause", "onBuffer", "onBufferEnd", "onEnded", "onReady", "loop", "playerVars", "onUnstarted", "UNSTARTED", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "isPlaylist", "container", "isReady", "playing", "muted", "playsinline", "controls", "config", "onError", "embedOptions", "id", "YT", "amount", "keepPlaying", "fraction", "rate", "display", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAkB,CAAE,EACxBd,EAASc,EAAiB,CACxB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAe,EACzCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,qCACVC,EAAa,KACbC,EAAmB,0BACnBC,EAAiB,wCACjBC,EAAqB,4BACrBC,EAAiB,wBACjBC,EAAgB,mCACtB,MAAMd,UAAgBE,EAAa,SAAU,CAC3C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,gBAAkBiB,GAAQ,CAC5C,GAAIA,aAAe,MACjB,MAAO,CACL,SAAU,WACV,SAAUA,EAAI,IAAI,KAAK,KAAK,EAAE,KAAK,GAAG,CACvC,EAEH,GAAIJ,EAAe,KAAKI,CAAG,EAAG,CAC5B,KAAM,CAAG,CAAAC,CAAU,EAAID,EAAI,MAAMJ,CAAc,EAC/C,MAAO,CACL,SAAU,WACV,KAAMK,EAAW,QAAQ,MAAO,IAAI,CACrC,CACT,CACM,GAAIJ,EAAmB,KAAKG,CAAG,EAAG,CAChC,KAAM,CAAG,CAAAE,CAAQ,EAAIF,EAAI,MAAMH,CAAkB,EACjD,MAAO,CACL,SAAU,eACV,KAAMK,CACP,CACT,CACM,MAAO,CAAE,CACf,CAAK,EACDnB,EAAc,KAAM,gBAAkBoB,GAAU,CAC9C,KAAM,CAAE,KAAAC,CAAI,EAAKD,EACX,CAAE,OAAAE,EAAQ,QAAAC,EAAS,SAAAC,EAAU,YAAAC,EAAa,QAAAC,EAAS,QAAAC,EAAS,KAAAC,EAAM,OAAQ,CAAE,WAAAC,EAAY,YAAAC,CAAW,CAAI,EAAG,KAAK,MAC/G,CAAE,UAAAC,EAAW,QAAAC,EAAS,OAAAC,EAAQ,UAAAC,EAAW,MAAAC,EAAO,KAAAC,GAAS,OAAOzB,CAAU,EAAE,YAWlF,GAVIU,IAASU,GACXD,EAAa,EACXT,IAASW,IACXV,EAAQ,EACRG,EAAa,GAEXJ,IAASY,GACXV,EAAS,EACPF,IAASa,GACXV,EAAU,EACRH,IAASc,EAAO,CAClB,MAAME,EAAa,CAAC,CAAC,KAAK,WAAW,aAAa,EAC9CT,GAAQ,CAACS,IACPR,EAAW,MACb,KAAK,OAAOA,EAAW,KAAK,EAE5B,KAAK,KAAM,GAGfH,EAAS,CACjB,CACUL,IAASe,GACXT,EAAS,CACjB,CAAK,EACD3B,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,WAAW,MAAM,CAC5B,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,WAAW,QAAQ,CAC9B,CAAK,EACDA,EAAc,KAAM,MAAQsC,GAAc,CACxC,KAAK,UAAYA,CACvB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,MAAMrB,EAAK,CACT,MAAI,CAACA,GAAOA,aAAe,OAASJ,EAAe,KAAKI,CAAG,EAClD,KAEFA,EAAI,MAAMT,EAAgB,iBAAiB,EAAE,CAAC,CACzD,CACE,KAAKS,EAAKsB,EAAS,CACjB,KAAM,CAAE,QAAAC,EAAS,MAAAC,EAAO,YAAAC,EAAa,SAAAC,EAAU,KAAAf,EAAM,OAAAgB,EAAQ,QAAAC,GAAY,KAAK,MACxE,CAAE,WAAAhB,EAAY,aAAAiB,CAAY,EAAKF,EAC/BG,EAAK,KAAK,MAAM9B,CAAG,EACzB,GAAIsB,EAAS,CACX,GAAI1B,EAAe,KAAKI,CAAG,GAAKH,EAAmB,KAAKG,CAAG,GAAKA,aAAe,MAAO,CACpF,KAAK,OAAO,aAAa,KAAK,cAAcA,CAAG,CAAC,EAChD,MACR,CACM,KAAK,OAAO,aAAa,CACvB,QAAS8B,EACT,gBAAkBzC,EAAa,gBAAgBW,CAAG,GAAKY,EAAW,MAClE,cAAgBvB,EAAa,cAAcW,CAAG,GAAKY,EAAW,GACtE,CAAO,EACD,MACN,IACQvB,EAAa,QAAQI,EAASC,EAAYC,EAAmBoC,GAAOA,EAAG,MAAM,EAAE,KAAMA,GAAO,CACzF,KAAK,YAEV,KAAK,OAAS,IAAIA,EAAG,OAAO,KAAK,UAAW,CAC1C,MAAO,OACP,OAAQ,OACR,QAASD,EACT,WAAY,CACV,SAAUP,EAAU,EAAI,EACxB,KAAMC,EAAQ,EAAI,EAClB,SAAUE,EAAW,EAAI,EACzB,SAAWrC,EAAa,gBAAgBW,CAAG,EAC3C,OAASX,EAAa,cAAcW,CAAG,EACvC,OAAQ,OAAO,SAAS,OACxB,YAAayB,EAAc,EAAI,EAC/B,GAAG,KAAK,cAAczB,CAAG,EACzB,GAAGY,CACJ,EACD,OAAQ,CACN,QAAS,IAAM,CACTD,GACF,KAAK,OAAO,QAAQ,EAAI,EAE1B,KAAK,MAAM,QAAS,CACrB,EACD,qBAAuBR,GAAU,KAAK,MAAM,qBAAqBA,EAAM,IAAI,EAC3E,wBAA0BA,GAAU,KAAK,MAAM,wBAAwBA,CAAK,EAC5E,cAAe,KAAK,cACpB,QAAUA,GAAUyB,EAAQzB,EAAM,IAAI,CACvC,EACD,KAAML,EAAe,KAAKE,CAAG,EAAID,EAAgB,OACjD,GAAG8B,CACX,CAAO,EACF,EAAED,CAAO,EACNC,EAAa,QACf,QAAQ,KAAK,6HAAkI,CAErJ,CACE,MAAO,CACL,KAAK,WAAW,WAAW,CAC/B,CACE,OAAQ,CACN,KAAK,WAAW,YAAY,CAChC,CACE,MAAO,CACA,SAAS,KAAK,SAAS,KAAK,WAAW,WAAW,CAAC,GAExD,KAAK,WAAW,WAAW,CAC/B,CACE,OAAOG,EAAQC,EAAc,GAAO,CAClC,KAAK,WAAW,SAAUD,CAAM,EAC5B,CAACC,GAAe,CAAC,KAAK,MAAM,SAC9B,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,EAAW,GAAG,CAC/C,CACE,gBAAgBC,EAAM,CACpB,KAAK,WAAW,kBAAmBA,CAAI,CAC3C,CACE,QAAQxB,EAAM,CACZ,KAAK,WAAW,UAAWA,CAAI,CACnC,CACE,aAAc,CACZ,OAAO,KAAK,WAAW,aAAa,CACxC,CACE,gBAAiB,CACf,OAAO,KAAK,WAAW,gBAAgB,CAC3C,CACE,kBAAmB,CACjB,OAAO,KAAK,WAAW,wBAAwB,EAAI,KAAK,YAAa,CACzE,CACE,QAAS,CACP,KAAM,CAAE,QAAAyB,GAAY,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACD,EACD,OAAuBjD,EAAa,QAAQ,cAAc,MAAO,CAAE,MAAAkD,CAAK,EAAoBlD,EAAa,QAAQ,cAAc,MAAO,CAAE,IAAK,KAAK,GAAG,CAAE,CAAC,CAC5J,CACA,CACAJ,EAAcE,EAAS,cAAe,SAAS,EAC/CF,EAAcE,EAAS,UAAWM,EAAgB,QAAQ,OAAO", "x_google_ignoreList": [0]}