{"version": 3, "file": "DailyMotion-7O_M3DVb.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/DailyMotion.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar DailyMotion_exports = {};\n__export(DailyMotion_exports, {\n  default: () => DailyMotion\n});\nmodule.exports = __toCommonJS(DailyMotion_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://api.dmcdn.net/all.js\";\nconst SDK_GLOBAL = \"DM\";\nconst SDK_GLOBAL_READY = \"dmAsyncInit\";\nclass DailyMotion extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"onDurationChange\", () => {\n      const duration = this.getDuration();\n      this.props.onDuration(duration);\n    });\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"setMuted\", true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"setMuted\", false);\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    const { controls, config, onError, playing } = this.props;\n    const [, id] = url.match(import_patterns.MATCH_URL_DAILYMOTION);\n    if (this.player) {\n      this.player.load(id, {\n        start: (0, import_utils.parseStartTime)(url),\n        autoplay: playing\n      });\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY, (DM) => DM.player).then((DM) => {\n      if (!this.container)\n        return;\n      const Player = DM.player;\n      this.player = new Player(this.container, {\n        width: \"100%\",\n        height: \"100%\",\n        video: id,\n        params: {\n          controls,\n          autoplay: this.props.playing,\n          mute: this.props.muted,\n          start: (0, import_utils.parseStartTime)(url),\n          origin: window.location.origin,\n          ...config.params\n        },\n        events: {\n          apiready: this.props.onReady,\n          seeked: () => this.props.onSeek(this.player.currentTime),\n          video_end: this.props.onEnded,\n          durationchange: this.onDurationChange,\n          pause: this.props.onPause,\n          playing: this.props.onPlay,\n          waiting: this.props.onBuffer,\n          error: (event) => onError(event)\n        }\n      });\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.player.duration || null;\n  }\n  getCurrentTime() {\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.player.bufferedTime;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style }, /* @__PURE__ */ import_react.default.createElement(\"div\", { ref: this.ref }));\n  }\n}\n__publicField(DailyMotion, \"displayName\", \"DailyMotion\");\n__publicField(DailyMotion, \"canPlay\", import_patterns.canPlay.dailymotion);\n__publicField(DailyMotion, \"loopOnEnded\", true);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "DailyMotion_exports", "DailyMotion", "DailyMotion_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "duration", "container", "url", "controls", "config", "onError", "playing", "id", "DM", "Player", "event", "seconds", "keepPlaying", "fraction", "display", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAsB,CAAE,EAC5Bd,EAASc,EAAqB,CAC5B,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAmB,EAC7CG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,+BACVC,EAAa,KACbC,EAAmB,cACzB,MAAMV,UAAoBE,EAAa,SAAU,CAC/C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,mBAAoB,IAAM,CAC5C,MAAMa,EAAW,KAAK,YAAa,EACnC,KAAK,MAAM,WAAWA,CAAQ,CACpC,CAAK,EACDb,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,WAAW,WAAY,EAAI,CACtC,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,WAAW,WAAY,EAAK,CACvC,CAAK,EACDA,EAAc,KAAM,MAAQc,GAAc,CACxC,KAAK,UAAYA,CACvB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKC,EAAK,CACR,KAAM,CAAE,SAAAC,EAAU,OAAAC,EAAQ,QAAAC,EAAS,QAAAC,CAAO,EAAK,KAAK,MAC9C,CAAA,CAAGC,CAAE,EAAIL,EAAI,MAAMP,EAAgB,qBAAqB,EAC9D,GAAI,KAAK,OAAQ,CACf,KAAK,OAAO,KAAKY,EAAI,CACnB,SAAWd,EAAa,gBAAgBS,CAAG,EAC3C,SAAUI,CAClB,CAAO,EACD,MACN,IACQb,EAAa,QAAQI,EAASC,EAAYC,EAAmBS,GAAOA,EAAG,MAAM,EAAE,KAAMA,GAAO,CAC9F,GAAI,CAAC,KAAK,UACR,OACF,MAAMC,EAASD,EAAG,OAClB,KAAK,OAAS,IAAIC,EAAO,KAAK,UAAW,CACvC,MAAO,OACP,OAAQ,OACR,MAAOF,EACP,OAAQ,CACN,SAAAJ,EACA,SAAU,KAAK,MAAM,QACrB,KAAM,KAAK,MAAM,MACjB,SAAWV,EAAa,gBAAgBS,CAAG,EAC3C,OAAQ,OAAO,SAAS,OACxB,GAAGE,EAAO,MACX,EACD,OAAQ,CACN,SAAU,KAAK,MAAM,QACrB,OAAQ,IAAM,KAAK,MAAM,OAAO,KAAK,OAAO,WAAW,EACvD,UAAW,KAAK,MAAM,QACtB,eAAgB,KAAK,iBACrB,MAAO,KAAK,MAAM,QAClB,QAAS,KAAK,MAAM,OACpB,QAAS,KAAK,MAAM,SACpB,MAAQM,GAAUL,EAAQK,CAAK,CACzC,CACA,CAAO,CACF,EAAEL,CAAO,CACd,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACT,CACE,OAAOM,EAASC,EAAc,GAAM,CAClC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,CAAQ,CACzC,CACE,aAAc,CACZ,OAAO,KAAK,OAAO,UAAY,IACnC,CACE,gBAAiB,CACf,OAAO,KAAK,OAAO,WACvB,CACE,kBAAmB,CACjB,OAAO,KAAK,OAAO,YACvB,CACE,QAAS,CACP,KAAM,CAAE,QAAAC,GAAY,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACD,EACD,OAAuBvB,EAAa,QAAQ,cAAc,MAAO,CAAE,MAAAwB,CAAK,EAAoBxB,EAAa,QAAQ,cAAc,MAAO,CAAE,IAAK,KAAK,GAAG,CAAE,CAAC,CAC5J,CACA,CACAJ,EAAcE,EAAa,cAAe,aAAa,EACvDF,EAAcE,EAAa,UAAWM,EAAgB,QAAQ,WAAW,EACzER,EAAcE,EAAa,cAAe,EAAI", "x_google_ignoreList": [0]}