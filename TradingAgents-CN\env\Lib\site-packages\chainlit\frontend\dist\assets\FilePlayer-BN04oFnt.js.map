{"version": 3, "file": "FilePlayer-BN04oFnt.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/FilePlayer.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar FilePlayer_exports = {};\n__export(FilePlayer_exports, {\n  default: () => FilePlayer\n});\nmodule.exports = __toCommonJS(FilePlayer_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst HAS_NAVIGATOR = typeof navigator !== \"undefined\";\nconst IS_IPAD_PRO = HAS_NAVIGATOR && navigator.platform === \"MacIntel\" && navigator.maxTouchPoints > 1;\nconst IS_IOS = HAS_NAVIGATOR && (/iPad|iPhone|iPod/.test(navigator.userAgent) || IS_IPAD_PRO) && !window.MSStream;\nconst IS_SAFARI = HAS_NAVIGATOR && /^((?!chrome|android).)*safari/i.test(navigator.userAgent) && !window.MSStream;\nconst HLS_SDK_URL = \"https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js\";\nconst HLS_GLOBAL = \"Hls\";\nconst DASH_SDK_URL = \"https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js\";\nconst DASH_GLOBAL = \"dashjs\";\nconst FLV_SDK_URL = \"https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js\";\nconst FLV_GLOBAL = \"flvjs\";\nconst MATCH_DROPBOX_URL = /www\\.dropbox\\.com\\/.+/;\nconst MATCH_CLOUDFLARE_STREAM = /https:\\/\\/watch\\.cloudflarestream\\.com\\/([a-z0-9]+)/;\nconst REPLACE_CLOUDFLARE_STREAM = \"https://videodelivery.net/{id}/manifest/video.m3u8\";\nclass FilePlayer extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Proxy methods to prevent listener leaks\n    __publicField(this, \"onReady\", (...args) => this.props.onReady(...args));\n    __publicField(this, \"onPlay\", (...args) => this.props.onPlay(...args));\n    __publicField(this, \"onBuffer\", (...args) => this.props.onBuffer(...args));\n    __publicField(this, \"onBufferEnd\", (...args) => this.props.onBufferEnd(...args));\n    __publicField(this, \"onPause\", (...args) => this.props.onPause(...args));\n    __publicField(this, \"onEnded\", (...args) => this.props.onEnded(...args));\n    __publicField(this, \"onError\", (...args) => this.props.onError(...args));\n    __publicField(this, \"onPlayBackRateChange\", (event) => this.props.onPlaybackRateChange(event.target.playbackRate));\n    __publicField(this, \"onEnablePIP\", (...args) => this.props.onEnablePIP(...args));\n    __publicField(this, \"onDisablePIP\", (e) => {\n      const { onDisablePIP, playing } = this.props;\n      onDisablePIP(e);\n      if (playing) {\n        this.play();\n      }\n    });\n    __publicField(this, \"onPresentationModeChange\", (e) => {\n      if (this.player && (0, import_utils.supportsWebKitPresentationMode)(this.player)) {\n        const { webkitPresentationMode } = this.player;\n        if (webkitPresentationMode === \"picture-in-picture\") {\n          this.onEnablePIP(e);\n        } else if (webkitPresentationMode === \"inline\") {\n          this.onDisablePIP(e);\n        }\n      }\n    });\n    __publicField(this, \"onSeek\", (e) => {\n      this.props.onSeek(e.target.currentTime);\n    });\n    __publicField(this, \"mute\", () => {\n      this.player.muted = true;\n    });\n    __publicField(this, \"unmute\", () => {\n      this.player.muted = false;\n    });\n    __publicField(this, \"renderSourceElement\", (source, index) => {\n      if (typeof source === \"string\") {\n        return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, src: source });\n      }\n      return /* @__PURE__ */ import_react.default.createElement(\"source\", { key: index, ...source });\n    });\n    __publicField(this, \"renderTrack\", (track, index) => {\n      return /* @__PURE__ */ import_react.default.createElement(\"track\", { key: index, ...track });\n    });\n    __publicField(this, \"ref\", (player) => {\n      if (this.player) {\n        this.prevPlayer = this.player;\n      }\n      this.player = player;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n    this.addListeners(this.player);\n    const src = this.getSource(this.props.url);\n    if (src) {\n      this.player.src = src;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      this.player.load();\n    }\n  }\n  componentDidUpdate(prevProps) {\n    if (this.shouldUseAudio(this.props) !== this.shouldUseAudio(prevProps)) {\n      this.removeListeners(this.prevPlayer, prevProps.url);\n      this.addListeners(this.player);\n    }\n    if (this.props.url !== prevProps.url && !(0, import_utils.isMediaStream)(this.props.url) && !(this.props.url instanceof Array)) {\n      this.player.srcObject = null;\n    }\n  }\n  componentWillUnmount() {\n    this.player.removeAttribute(\"src\");\n    this.removeListeners(this.player);\n    if (this.hls) {\n      this.hls.destroy();\n    }\n  }\n  addListeners(player) {\n    const { url, playsinline } = this.props;\n    player.addEventListener(\"play\", this.onPlay);\n    player.addEventListener(\"waiting\", this.onBuffer);\n    player.addEventListener(\"playing\", this.onBufferEnd);\n    player.addEventListener(\"pause\", this.onPause);\n    player.addEventListener(\"seeked\", this.onSeek);\n    player.addEventListener(\"ended\", this.onEnded);\n    player.addEventListener(\"error\", this.onError);\n    player.addEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.addEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.addEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.addEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.addEventListener(\"canplay\", this.onReady);\n    }\n    if (playsinline) {\n      player.setAttribute(\"playsinline\", \"\");\n      player.setAttribute(\"webkit-playsinline\", \"\");\n      player.setAttribute(\"x5-playsinline\", \"\");\n    }\n  }\n  removeListeners(player, url) {\n    player.removeEventListener(\"canplay\", this.onReady);\n    player.removeEventListener(\"play\", this.onPlay);\n    player.removeEventListener(\"waiting\", this.onBuffer);\n    player.removeEventListener(\"playing\", this.onBufferEnd);\n    player.removeEventListener(\"pause\", this.onPause);\n    player.removeEventListener(\"seeked\", this.onSeek);\n    player.removeEventListener(\"ended\", this.onEnded);\n    player.removeEventListener(\"error\", this.onError);\n    player.removeEventListener(\"ratechange\", this.onPlayBackRateChange);\n    player.removeEventListener(\"enterpictureinpicture\", this.onEnablePIP);\n    player.removeEventListener(\"leavepictureinpicture\", this.onDisablePIP);\n    player.removeEventListener(\"webkitpresentationmodechanged\", this.onPresentationModeChange);\n    if (!this.shouldUseHLS(url)) {\n      player.removeEventListener(\"canplay\", this.onReady);\n    }\n  }\n  shouldUseAudio(props) {\n    if (props.config.forceVideo) {\n      return false;\n    }\n    if (props.config.attributes.poster) {\n      return false;\n    }\n    return import_patterns.AUDIO_EXTENSIONS.test(props.url) || props.config.forceAudio;\n  }\n  shouldUseHLS(url) {\n    if (IS_SAFARI && this.props.config.forceSafariHLS || this.props.config.forceHLS) {\n      return true;\n    }\n    if (IS_IOS || this.props.config.forceDisableHls) {\n      return false;\n    }\n    return import_patterns.HLS_EXTENSIONS.test(url) || MATCH_CLOUDFLARE_STREAM.test(url);\n  }\n  shouldUseDASH(url) {\n    return import_patterns.DASH_EXTENSIONS.test(url) || this.props.config.forceDASH;\n  }\n  shouldUseFLV(url) {\n    return import_patterns.FLV_EXTENSIONS.test(url) || this.props.config.forceFLV;\n  }\n  load(url) {\n    const { hlsVersion, hlsOptions, dashVersion, flvVersion } = this.props.config;\n    if (this.hls) {\n      this.hls.destroy();\n    }\n    if (this.dash) {\n      this.dash.reset();\n    }\n    if (this.shouldUseHLS(url)) {\n      (0, import_utils.getSDK)(HLS_SDK_URL.replace(\"VERSION\", hlsVersion), HLS_GLOBAL).then((Hls) => {\n        this.hls = new Hls(hlsOptions);\n        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {\n          this.props.onReady();\n        });\n        this.hls.on(Hls.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.hls, Hls);\n        });\n        if (MATCH_CLOUDFLARE_STREAM.test(url)) {\n          const id = url.match(MATCH_CLOUDFLARE_STREAM)[1];\n          this.hls.loadSource(REPLACE_CLOUDFLARE_STREAM.replace(\"{id}\", id));\n        } else {\n          this.hls.loadSource(url);\n        }\n        this.hls.attachMedia(this.player);\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseDASH(url)) {\n      (0, import_utils.getSDK)(DASH_SDK_URL.replace(\"VERSION\", dashVersion), DASH_GLOBAL).then((dashjs) => {\n        this.dash = dashjs.MediaPlayer().create();\n        this.dash.initialize(this.player, url, this.props.playing);\n        this.dash.on(\"error\", this.props.onError);\n        if (parseInt(dashVersion) < 3) {\n          this.dash.getDebug().setLogToBrowserConsole(false);\n        } else {\n          this.dash.updateSettings({ debug: { logLevel: dashjs.Debug.LOG_LEVEL_NONE } });\n        }\n        this.props.onLoaded();\n      });\n    }\n    if (this.shouldUseFLV(url)) {\n      (0, import_utils.getSDK)(FLV_SDK_URL.replace(\"VERSION\", flvVersion), FLV_GLOBAL).then((flvjs) => {\n        this.flv = flvjs.createPlayer({ type: \"flv\", url });\n        this.flv.attachMediaElement(this.player);\n        this.flv.on(flvjs.Events.ERROR, (e, data) => {\n          this.props.onError(e, data, this.flv, flvjs);\n        });\n        this.flv.load();\n        this.props.onLoaded();\n      });\n    }\n    if (url instanceof Array) {\n      this.player.load();\n    } else if ((0, import_utils.isMediaStream)(url)) {\n      try {\n        this.player.srcObject = url;\n      } catch (e) {\n        this.player.src = window.URL.createObjectURL(url);\n      }\n    }\n  }\n  play() {\n    const promise = this.player.play();\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.player.pause();\n  }\n  stop() {\n    this.player.removeAttribute(\"src\");\n    if (this.dash) {\n      this.dash.reset();\n    }\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.player.currentTime = seconds;\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.player.volume = fraction;\n  }\n  enablePIP() {\n    if (this.player.requestPictureInPicture && document.pictureInPictureElement !== this.player) {\n      this.player.requestPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"picture-in-picture\") {\n      this.player.webkitSetPresentationMode(\"picture-in-picture\");\n    }\n  }\n  disablePIP() {\n    if (document.exitPictureInPicture && document.pictureInPictureElement === this.player) {\n      document.exitPictureInPicture();\n    } else if ((0, import_utils.supportsWebKitPresentationMode)(this.player) && this.player.webkitPresentationMode !== \"inline\") {\n      this.player.webkitSetPresentationMode(\"inline\");\n    }\n  }\n  setPlaybackRate(rate) {\n    try {\n      this.player.playbackRate = rate;\n    } catch (error) {\n      this.props.onError(error);\n    }\n  }\n  getDuration() {\n    if (!this.player)\n      return null;\n    const { duration, seekable } = this.player;\n    if (duration === Infinity && seekable.length > 0) {\n      return seekable.end(seekable.length - 1);\n    }\n    return duration;\n  }\n  getCurrentTime() {\n    if (!this.player)\n      return null;\n    return this.player.currentTime;\n  }\n  getSecondsLoaded() {\n    if (!this.player)\n      return null;\n    const { buffered } = this.player;\n    if (buffered.length === 0) {\n      return 0;\n    }\n    const end = buffered.end(buffered.length - 1);\n    const duration = this.getDuration();\n    if (end > duration) {\n      return duration;\n    }\n    return end;\n  }\n  getSource(url) {\n    const useHLS = this.shouldUseHLS(url);\n    const useDASH = this.shouldUseDASH(url);\n    const useFLV = this.shouldUseFLV(url);\n    if (url instanceof Array || (0, import_utils.isMediaStream)(url) || useHLS || useDASH || useFLV) {\n      return void 0;\n    }\n    if (MATCH_DROPBOX_URL.test(url)) {\n      return url.replace(\"www.dropbox.com\", \"dl.dropboxusercontent.com\");\n    }\n    return url;\n  }\n  render() {\n    const { url, playing, loop, controls, muted, config, width, height } = this.props;\n    const useAudio = this.shouldUseAudio(this.props);\n    const Element = useAudio ? \"audio\" : \"video\";\n    const style = {\n      width: width === \"auto\" ? width : \"100%\",\n      height: height === \"auto\" ? height : \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      Element,\n      {\n        ref: this.ref,\n        src: this.getSource(url),\n        style,\n        preload: \"auto\",\n        autoPlay: playing || void 0,\n        controls,\n        muted,\n        loop,\n        ...config.attributes\n      },\n      url instanceof Array && url.map(this.renderSourceElement),\n      config.tracks.map(this.renderTrack)\n    );\n  }\n}\n__publicField(FilePlayer, \"displayName\", \"FilePlayer\");\n__publicField(FilePlayer, \"canPlay\", import_patterns.canPlay.file);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "FilePlayer_exports", "FilePlayer", "FilePlayer_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "HAS_NAVIGATOR", "IS_IPAD_PRO", "IS_IOS", "IS_SAFARI", "HLS_SDK_URL", "HLS_GLOBAL", "DASH_SDK_URL", "DASH_GLOBAL", "FLV_SDK_URL", "FLV_GLOBAL", "MATCH_DROPBOX_URL", "MATCH_CLOUDFLARE_STREAM", "REPLACE_CLOUDFLARE_STREAM", "args", "event", "onDisablePIP", "playing", "webkitPresentationMode", "source", "index", "track", "player", "src", "prevProps", "url", "playsinline", "props", "hlsVersion", "hlsOptions", "dashVersion", "flvVersion", "Hls", "e", "data", "id", "dashjs", "flvjs", "promise", "seconds", "keepPlaying", "fraction", "rate", "error", "duration", "seekable", "buffered", "end", "useHLS", "useDASH", "useFLV", "loop", "controls", "muted", "config", "width", "height", "Element", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAqB,CAAE,EAC3Bd,EAASc,EAAoB,CAC3B,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAkB,EAC5CG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAgB,OAAO,UAAc,IACrCC,EAAcD,GAAiB,UAAU,WAAa,YAAc,UAAU,eAAiB,EAC/FE,EAASF,IAAkB,mBAAmB,KAAK,UAAU,SAAS,GAAKC,IAAgB,CAAC,OAAO,SACnGE,EAAYH,GAAiB,iCAAiC,KAAK,UAAU,SAAS,GAAK,CAAC,OAAO,SACnGI,EAAc,8DACdC,EAAa,MACbC,EAAe,wEACfC,EAAc,SACdC,EAAc,8DACdC,EAAa,QACbC,EAAoB,wBACpBC,EAA0B,sDAC1BC,EAA4B,qDAClC,MAAMpB,UAAmBE,EAAa,SAAU,CAC9C,aAAc,CACZ,MAAM,GAAG,SAAS,EAElBJ,EAAc,KAAM,UAAW,IAAIuB,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvEvB,EAAc,KAAM,SAAU,IAAIuB,IAAS,KAAK,MAAM,OAAO,GAAGA,CAAI,CAAC,EACrEvB,EAAc,KAAM,WAAY,IAAIuB,IAAS,KAAK,MAAM,SAAS,GAAGA,CAAI,CAAC,EACzEvB,EAAc,KAAM,cAAe,IAAIuB,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,CAAC,EAC/EvB,EAAc,KAAM,UAAW,IAAIuB,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvEvB,EAAc,KAAM,UAAW,IAAIuB,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvEvB,EAAc,KAAM,UAAW,IAAIuB,IAAS,KAAK,MAAM,QAAQ,GAAGA,CAAI,CAAC,EACvEvB,EAAc,KAAM,uBAAyBwB,GAAU,KAAK,MAAM,qBAAqBA,EAAM,OAAO,YAAY,CAAC,EACjHxB,EAAc,KAAM,cAAe,IAAIuB,IAAS,KAAK,MAAM,YAAY,GAAGA,CAAI,CAAC,EAC/EvB,EAAc,KAAM,eAAiB,GAAM,CACzC,KAAM,CAAE,aAAAyB,EAAc,QAAAC,CAAS,EAAG,KAAK,MACvCD,EAAa,CAAC,EACVC,GACF,KAAK,KAAM,CAEnB,CAAK,EACD1B,EAAc,KAAM,2BAA6B,GAAM,CACrD,GAAI,KAAK,WAAcM,EAAa,gCAAgC,KAAK,MAAM,EAAG,CAChF,KAAM,CAAE,uBAAAqB,GAA2B,KAAK,OACpCA,IAA2B,qBAC7B,KAAK,YAAY,CAAC,EACTA,IAA2B,UACpC,KAAK,aAAa,CAAC,CAE7B,CACA,CAAK,EACD3B,EAAc,KAAM,SAAW,GAAM,CACnC,KAAK,MAAM,OAAO,EAAE,OAAO,WAAW,CAC5C,CAAK,EACDA,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,OAAO,MAAQ,EAC1B,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,OAAO,MAAQ,EAC1B,CAAK,EACDA,EAAc,KAAM,sBAAuB,CAAC4B,EAAQC,IAC9C,OAAOD,GAAW,SACGxB,EAAa,QAAQ,cAAc,SAAU,CAAE,IAAKyB,EAAO,IAAKD,EAAQ,EAE1ExB,EAAa,QAAQ,cAAc,SAAU,CAAE,IAAKyB,EAAO,GAAGD,EAAQ,CAC9F,EACD5B,EAAc,KAAM,cAAe,CAAC8B,EAAOD,IAClBzB,EAAa,QAAQ,cAAc,QAAS,CAAE,IAAKyB,EAAO,GAAGC,EAAO,CAC5F,EACD9B,EAAc,KAAM,MAAQ+B,GAAW,CACjC,KAAK,SACP,KAAK,WAAa,KAAK,QAEzB,KAAK,OAASA,CACpB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,EAC7C,KAAK,aAAa,KAAK,MAAM,EAC7B,MAAMC,EAAM,KAAK,UAAU,KAAK,MAAM,GAAG,EACrCA,IACF,KAAK,OAAO,IAAMA,IAEhBpB,GAAU,KAAK,MAAM,OAAO,kBAC9B,KAAK,OAAO,KAAM,CAExB,CACE,mBAAmBqB,EAAW,CACxB,KAAK,eAAe,KAAK,KAAK,IAAM,KAAK,eAAeA,CAAS,IACnE,KAAK,gBAAgB,KAAK,WAAYA,EAAU,GAAG,EACnD,KAAK,aAAa,KAAK,MAAM,GAE3B,KAAK,MAAM,MAAQA,EAAU,KAAO,IAAK3B,EAAa,eAAe,KAAK,MAAM,GAAG,GAAK,EAAE,KAAK,MAAM,eAAe,SACtH,KAAK,OAAO,UAAY,KAE9B,CACE,sBAAuB,CACrB,KAAK,OAAO,gBAAgB,KAAK,EACjC,KAAK,gBAAgB,KAAK,MAAM,EAC5B,KAAK,KACP,KAAK,IAAI,QAAS,CAExB,CACE,aAAayB,EAAQ,CACnB,KAAM,CAAE,IAAAG,EAAK,YAAAC,CAAa,EAAG,KAAK,MAClCJ,EAAO,iBAAiB,OAAQ,KAAK,MAAM,EAC3CA,EAAO,iBAAiB,UAAW,KAAK,QAAQ,EAChDA,EAAO,iBAAiB,UAAW,KAAK,WAAW,EACnDA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,SAAU,KAAK,MAAM,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,QAAS,KAAK,OAAO,EAC7CA,EAAO,iBAAiB,aAAc,KAAK,oBAAoB,EAC/DA,EAAO,iBAAiB,wBAAyB,KAAK,WAAW,EACjEA,EAAO,iBAAiB,wBAAyB,KAAK,YAAY,EAClEA,EAAO,iBAAiB,gCAAiC,KAAK,wBAAwB,EACjF,KAAK,aAAaG,CAAG,GACxBH,EAAO,iBAAiB,UAAW,KAAK,OAAO,EAE7CI,IACFJ,EAAO,aAAa,cAAe,EAAE,EACrCA,EAAO,aAAa,qBAAsB,EAAE,EAC5CA,EAAO,aAAa,iBAAkB,EAAE,EAE9C,CACE,gBAAgBA,EAAQG,EAAK,CAC3BH,EAAO,oBAAoB,UAAW,KAAK,OAAO,EAClDA,EAAO,oBAAoB,OAAQ,KAAK,MAAM,EAC9CA,EAAO,oBAAoB,UAAW,KAAK,QAAQ,EACnDA,EAAO,oBAAoB,UAAW,KAAK,WAAW,EACtDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,SAAU,KAAK,MAAM,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,QAAS,KAAK,OAAO,EAChDA,EAAO,oBAAoB,aAAc,KAAK,oBAAoB,EAClEA,EAAO,oBAAoB,wBAAyB,KAAK,WAAW,EACpEA,EAAO,oBAAoB,wBAAyB,KAAK,YAAY,EACrEA,EAAO,oBAAoB,gCAAiC,KAAK,wBAAwB,EACpF,KAAK,aAAaG,CAAG,GACxBH,EAAO,oBAAoB,UAAW,KAAK,OAAO,CAExD,CACE,eAAeK,EAAO,CAIpB,OAHIA,EAAM,OAAO,YAGbA,EAAM,OAAO,WAAW,OACnB,GAEF5B,EAAgB,iBAAiB,KAAK4B,EAAM,GAAG,GAAKA,EAAM,OAAO,UAC5E,CACE,aAAaF,EAAK,CAChB,OAAIrB,GAAa,KAAK,MAAM,OAAO,gBAAkB,KAAK,MAAM,OAAO,SAC9D,GAELD,GAAU,KAAK,MAAM,OAAO,gBACvB,GAEFJ,EAAgB,eAAe,KAAK0B,CAAG,GAAKb,EAAwB,KAAKa,CAAG,CACvF,CACE,cAAcA,EAAK,CACjB,OAAO1B,EAAgB,gBAAgB,KAAK0B,CAAG,GAAK,KAAK,MAAM,OAAO,SAC1E,CACE,aAAaA,EAAK,CAChB,OAAO1B,EAAgB,eAAe,KAAK0B,CAAG,GAAK,KAAK,MAAM,OAAO,QACzE,CACE,KAAKA,EAAK,CACR,KAAM,CAAE,WAAAG,EAAY,WAAAC,EAAY,YAAAC,EAAa,WAAAC,GAAe,KAAK,MAAM,OAkDvE,GAjDI,KAAK,KACP,KAAK,IAAI,QAAS,EAEhB,KAAK,MACP,KAAK,KAAK,MAAO,EAEf,KAAK,aAAaN,CAAG,MACnB5B,EAAa,QAAQQ,EAAY,QAAQ,UAAWuB,CAAU,EAAGtB,CAAU,EAAE,KAAM0B,GAAQ,CAQ7F,GAPA,KAAK,IAAM,IAAIA,EAAIH,CAAU,EAC7B,KAAK,IAAI,GAAGG,EAAI,OAAO,gBAAiB,IAAM,CAC5C,KAAK,MAAM,QAAS,CAC9B,CAAS,EACD,KAAK,IAAI,GAAGA,EAAI,OAAO,MAAO,CAACC,EAAGC,IAAS,CACzC,KAAK,MAAM,QAAQD,EAAGC,EAAM,KAAK,IAAKF,CAAG,CACnD,CAAS,EACGpB,EAAwB,KAAKa,CAAG,EAAG,CACrC,MAAMU,EAAKV,EAAI,MAAMb,CAAuB,EAAE,CAAC,EAC/C,KAAK,IAAI,WAAWC,EAA0B,QAAQ,OAAQsB,CAAE,CAAC,CAC3E,MACU,KAAK,IAAI,WAAWV,CAAG,EAEzB,KAAK,IAAI,YAAY,KAAK,MAAM,EAChC,KAAK,MAAM,SAAU,CAC7B,CAAO,EAEC,KAAK,cAAcA,CAAG,MACpB5B,EAAa,QAAQU,EAAa,QAAQ,UAAWuB,CAAW,EAAGtB,CAAW,EAAE,KAAM4B,GAAW,CACnG,KAAK,KAAOA,EAAO,YAAW,EAAG,OAAQ,EACzC,KAAK,KAAK,WAAW,KAAK,OAAQX,EAAK,KAAK,MAAM,OAAO,EACzD,KAAK,KAAK,GAAG,QAAS,KAAK,MAAM,OAAO,EACpC,SAASK,CAAW,EAAI,EAC1B,KAAK,KAAK,WAAW,uBAAuB,EAAK,EAEjD,KAAK,KAAK,eAAe,CAAE,MAAO,CAAE,SAAUM,EAAO,MAAM,cAAc,EAAI,EAE/E,KAAK,MAAM,SAAU,CAC7B,CAAO,EAEC,KAAK,aAAaX,CAAG,MACnB5B,EAAa,QAAQY,EAAY,QAAQ,UAAWsB,CAAU,EAAGrB,CAAU,EAAE,KAAM2B,GAAU,CAC/F,KAAK,IAAMA,EAAM,aAAa,CAAE,KAAM,MAAO,IAAAZ,EAAK,EAClD,KAAK,IAAI,mBAAmB,KAAK,MAAM,EACvC,KAAK,IAAI,GAAGY,EAAM,OAAO,MAAO,CAACJ,EAAGC,IAAS,CAC3C,KAAK,MAAM,QAAQD,EAAGC,EAAM,KAAK,IAAKG,CAAK,CACrD,CAAS,EACD,KAAK,IAAI,KAAM,EACf,KAAK,MAAM,SAAU,CAC7B,CAAO,EAECZ,aAAe,MACjB,KAAK,OAAO,KAAM,aACL5B,EAAa,eAAe4B,CAAG,EAC5C,GAAI,CACF,KAAK,OAAO,UAAYA,CACzB,MAAW,CACV,KAAK,OAAO,IAAM,OAAO,IAAI,gBAAgBA,CAAG,CACxD,CAEA,CACE,MAAO,CACL,MAAMa,EAAU,KAAK,OAAO,KAAM,EAC9BA,GACFA,EAAQ,MAAM,KAAK,MAAM,OAAO,CAEtC,CACE,OAAQ,CACN,KAAK,OAAO,MAAO,CACvB,CACE,MAAO,CACL,KAAK,OAAO,gBAAgB,KAAK,EAC7B,KAAK,MACP,KAAK,KAAK,MAAO,CAEvB,CACE,OAAOC,EAASC,EAAc,GAAM,CAClC,KAAK,OAAO,YAAcD,EACrBC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,OAAO,OAASA,CACzB,CACE,WAAY,CACN,KAAK,OAAO,yBAA2B,SAAS,0BAA4B,KAAK,OACnF,KAAK,OAAO,wBAAyB,KACxB5C,EAAa,gCAAgC,KAAK,MAAM,GAAK,KAAK,OAAO,yBAA2B,sBACjH,KAAK,OAAO,0BAA0B,oBAAoB,CAEhE,CACE,YAAa,CACP,SAAS,sBAAwB,SAAS,0BAA4B,KAAK,OAC7E,SAAS,qBAAsB,KAClBA,EAAa,gCAAgC,KAAK,MAAM,GAAK,KAAK,OAAO,yBAA2B,UACjH,KAAK,OAAO,0BAA0B,QAAQ,CAEpD,CACE,gBAAgB6C,EAAM,CACpB,GAAI,CACF,KAAK,OAAO,aAAeA,CAC5B,OAAQC,EAAO,CACd,KAAK,MAAM,QAAQA,CAAK,CAC9B,CACA,CACE,aAAc,CACZ,GAAI,CAAC,KAAK,OACR,OAAO,KACT,KAAM,CAAE,SAAAC,EAAU,SAAAC,CAAU,EAAG,KAAK,OACpC,OAAID,IAAa,KAAYC,EAAS,OAAS,EACtCA,EAAS,IAAIA,EAAS,OAAS,CAAC,EAElCD,CACX,CACE,gBAAiB,CACf,OAAK,KAAK,OAEH,KAAK,OAAO,YADV,IAEb,CACE,kBAAmB,CACjB,GAAI,CAAC,KAAK,OACR,OAAO,KACT,KAAM,CAAE,SAAAE,GAAa,KAAK,OAC1B,GAAIA,EAAS,SAAW,EACtB,MAAO,GAET,MAAMC,EAAMD,EAAS,IAAIA,EAAS,OAAS,CAAC,EACtCF,EAAW,KAAK,YAAa,EACnC,OAAIG,EAAMH,EACDA,EAEFG,CACX,CACE,UAAUtB,EAAK,CACb,MAAMuB,EAAS,KAAK,aAAavB,CAAG,EAC9BwB,EAAU,KAAK,cAAcxB,CAAG,EAChCyB,EAAS,KAAK,aAAazB,CAAG,EACpC,GAAI,EAAAA,aAAe,UAAa5B,EAAa,eAAe4B,CAAG,GAAKuB,GAAUC,GAAWC,GAGzF,OAAIvC,EAAkB,KAAKc,CAAG,EACrBA,EAAI,QAAQ,kBAAmB,2BAA2B,EAE5DA,CACX,CACE,QAAS,CACP,KAAM,CAAE,IAAAA,EAAK,QAAAR,EAAS,KAAAkC,EAAM,SAAAC,EAAU,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,EAAO,OAAAC,CAAQ,EAAG,KAAK,MAEtEC,EADW,KAAK,eAAe,KAAK,KAAK,EACpB,QAAU,QAC/BC,EAAQ,CACZ,MAAOH,IAAU,OAASA,EAAQ,OAClC,OAAQC,IAAW,OAASA,EAAS,MACtC,EACD,OAAuB7D,EAAa,QAAQ,cAC1C8D,EACA,CACE,IAAK,KAAK,IACV,IAAK,KAAK,UAAUhC,CAAG,EACvB,MAAAiC,EACA,QAAS,OACT,SAAUzC,GAAW,OACrB,SAAAmC,EACA,MAAAC,EACA,KAAAF,EACA,GAAGG,EAAO,UACX,EACD7B,aAAe,OAASA,EAAI,IAAI,KAAK,mBAAmB,EACxD6B,EAAO,OAAO,IAAI,KAAK,WAAW,CACnC,CACL,CACA,CACA/D,EAAcE,EAAY,cAAe,YAAY,EACrDF,EAAcE,EAAY,UAAWM,EAAgB,QAAQ,IAAI", "x_google_ignoreList": [0]}