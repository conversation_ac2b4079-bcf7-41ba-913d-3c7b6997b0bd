Metadata-Version: 2.1
Name: langchain-experimental
Version: 0.3.4
Summary: Building applications with LLMs through composability
Home-page: https://github.com/langchain-ai/langchain-experimental
License: MIT
Requires-Python: >=3.9,<4.0
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: langchain-community (>=0.3.0,<0.4.0)
Requires-Dist: langchain-core (>=0.3.28,<0.4.0)
Project-URL: Repository, https://github.com/langchain-ai/langchain-experimental
Project-URL: Release Notes, https://github.com/langchain-ai/langchain-experimental/releases
Project-URL: Source Code, https://github.com/langchain-ai/langchain-experimental/tree/main/libs/experimental
Description-Content-Type: text/markdown

# 🦜️🧪 LangChain Experimental

This package holds experimental LangChain code, intended for research and experimental
uses.

> [!WARNING]
> Portions of the code in this package may be dangerous if not properly deployed
> in a sandboxed environment. Please be wary of deploying experimental code
> to production unless you've taken appropriate precautions and
> have already discussed it with your security team.

Some of the code here may be marked with security notices. However,
given the exploratory and experimental nature of the code in this package,
the lack of a security notice on a piece of code does not mean that
the code in question does not require additional security considerations
in order to be safe to use.
