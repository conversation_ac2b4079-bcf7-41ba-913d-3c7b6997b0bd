{"version": 3, "file": "Vimeo-C6bmENiF.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Vimeo.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Vimeo_exports = {};\n__export(Vimeo_exports, {\n  default: () => Vimeo\n});\nmodule.exports = __toCommonJS(Vimeo_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://player.vimeo.com/api/player.js\";\nconst SDK_GLOBAL = \"Vimeo\";\nconst cleanUrl = (url) => {\n  return url.replace(\"/manage/videos\", \"\");\n};\nclass Vimeo extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Prevent checking isLoading when URL changes\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setMuted(true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.setMuted(false);\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    this.duration = null;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Vimeo2) => {\n      if (!this.container)\n        return;\n      const { playerOptions, title } = this.props.config;\n      this.player = new Vimeo2.Player(this.container, {\n        url: cleanUrl(url),\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        loop: this.props.loop,\n        playsinline: this.props.playsinline,\n        controls: this.props.controls,\n        ...playerOptions\n      });\n      this.player.ready().then(() => {\n        const iframe = this.container.querySelector(\"iframe\");\n        iframe.style.width = \"100%\";\n        iframe.style.height = \"100%\";\n        if (title) {\n          iframe.title = title;\n        }\n      }).catch(this.props.onError);\n      this.player.on(\"loaded\", () => {\n        this.props.onReady();\n        this.refreshDuration();\n      });\n      this.player.on(\"play\", () => {\n        this.props.onPlay();\n        this.refreshDuration();\n      });\n      this.player.on(\"pause\", this.props.onPause);\n      this.player.on(\"seeked\", (e) => this.props.onSeek(e.seconds));\n      this.player.on(\"ended\", this.props.onEnded);\n      this.player.on(\"error\", this.props.onError);\n      this.player.on(\"timeupdate\", ({ seconds }) => {\n        this.currentTime = seconds;\n      });\n      this.player.on(\"progress\", ({ seconds }) => {\n        this.secondsLoaded = seconds;\n      });\n      this.player.on(\"bufferstart\", this.props.onBuffer);\n      this.player.on(\"bufferend\", this.props.onBufferEnd);\n      this.player.on(\"playbackratechange\", (e) => this.props.onPlaybackRateChange(e.playbackRate));\n    }, this.props.onError);\n  }\n  refreshDuration() {\n    this.player.getDuration().then((duration) => {\n      this.duration = duration;\n    });\n  }\n  play() {\n    const promise = this.callPlayer(\"play\");\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.callPlayer(\"unload\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setMuted(muted) {\n    this.callPlayer(\"setMuted\", muted);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackRate\", rate);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      overflow: \"hidden\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        key: this.props.url,\n        ref: this.ref,\n        style\n      }\n    );\n  }\n}\n__publicField(Vimeo, \"displayName\", \"Vimeo\");\n__publicField(Vimeo, \"canPlay\", import_patterns.canPlay.vimeo);\n__publicField(Vimeo, \"forceLoad\", true);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Vimeo_exports", "Vimeo", "Vimeo_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "cleanUrl", "url", "container", "Vimeo2", "playerOptions", "title", "iframe", "e", "seconds", "duration", "promise", "keepPlaying", "fraction", "muted", "loop", "rate", "display", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAgB,CAAE,EACtBd,EAASc,EAAe,CACtB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAa,EACvCG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,yCACVC,EAAa,QACbC,EAAYC,GACTA,EAAI,QAAQ,iBAAkB,EAAE,EAEzC,MAAMX,UAAcE,EAAa,SAAU,CACzC,aAAc,CACZ,MAAM,GAAG,SAAS,EAElBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,WAAY,IAAI,EACpCA,EAAc,KAAM,cAAe,IAAI,EACvCA,EAAc,KAAM,gBAAiB,IAAI,EACzCA,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,SAAS,EAAI,CACxB,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,SAAS,EAAK,CACzB,CAAK,EACDA,EAAc,KAAM,MAAQc,GAAc,CACxC,KAAK,UAAYA,CACvB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKD,EAAK,CACR,KAAK,SAAW,QACZP,EAAa,QAAQI,EAASC,CAAU,EAAE,KAAMI,GAAW,CAC7D,GAAI,CAAC,KAAK,UACR,OACF,KAAM,CAAE,cAAAC,EAAe,MAAAC,CAAO,EAAG,KAAK,MAAM,OAC5C,KAAK,OAAS,IAAIF,EAAO,OAAO,KAAK,UAAW,CAC9C,IAAKH,EAASC,CAAG,EACjB,SAAU,KAAK,MAAM,QACrB,MAAO,KAAK,MAAM,MAClB,KAAM,KAAK,MAAM,KACjB,YAAa,KAAK,MAAM,YACxB,SAAU,KAAK,MAAM,SACrB,GAAGG,CACX,CAAO,EACD,KAAK,OAAO,MAAO,EAAC,KAAK,IAAM,CAC7B,MAAME,EAAS,KAAK,UAAU,cAAc,QAAQ,EACpDA,EAAO,MAAM,MAAQ,OACrBA,EAAO,MAAM,OAAS,OAClBD,IACFC,EAAO,MAAQD,EAElB,CAAA,EAAE,MAAM,KAAK,MAAM,OAAO,EAC3B,KAAK,OAAO,GAAG,SAAU,IAAM,CAC7B,KAAK,MAAM,QAAS,EACpB,KAAK,gBAAiB,CAC9B,CAAO,EACD,KAAK,OAAO,GAAG,OAAQ,IAAM,CAC3B,KAAK,MAAM,OAAQ,EACnB,KAAK,gBAAiB,CAC9B,CAAO,EACD,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,SAAWE,GAAM,KAAK,MAAM,OAAOA,EAAE,OAAO,CAAC,EAC5D,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,QAAS,KAAK,MAAM,OAAO,EAC1C,KAAK,OAAO,GAAG,aAAc,CAAC,CAAE,QAAAC,CAAO,IAAO,CAC5C,KAAK,YAAcA,CAC3B,CAAO,EACD,KAAK,OAAO,GAAG,WAAY,CAAC,CAAE,QAAAA,CAAO,IAAO,CAC1C,KAAK,cAAgBA,CAC7B,CAAO,EACD,KAAK,OAAO,GAAG,cAAe,KAAK,MAAM,QAAQ,EACjD,KAAK,OAAO,GAAG,YAAa,KAAK,MAAM,WAAW,EAClD,KAAK,OAAO,GAAG,qBAAuBD,GAAM,KAAK,MAAM,qBAAqBA,EAAE,YAAY,CAAC,CACjG,EAAO,KAAK,MAAM,OAAO,CACzB,CACE,iBAAkB,CAChB,KAAK,OAAO,YAAa,EAAC,KAAME,GAAa,CAC3C,KAAK,SAAWA,CACtB,CAAK,CACL,CACE,MAAO,CACL,MAAMC,EAAU,KAAK,WAAW,MAAM,EAClCA,GACFA,EAAQ,MAAM,KAAK,MAAM,OAAO,CAEtC,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACL,KAAK,WAAW,QAAQ,CAC5B,CACE,OAAOF,EAASG,EAAc,GAAM,CAClC,KAAK,WAAW,iBAAkBH,CAAO,EACpCG,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,CAAQ,CACzC,CACE,SAASC,EAAO,CACd,KAAK,WAAW,WAAYA,CAAK,CACrC,CACE,QAAQC,EAAM,CACZ,KAAK,WAAW,UAAWA,CAAI,CACnC,CACE,gBAAgBC,EAAM,CACpB,KAAK,WAAW,kBAAmBA,CAAI,CAC3C,CACE,aAAc,CACZ,OAAO,KAAK,QAChB,CACE,gBAAiB,CACf,OAAO,KAAK,WAChB,CACE,kBAAmB,CACjB,OAAO,KAAK,aAChB,CACE,QAAS,CACP,KAAM,CAAE,QAAAC,GAAY,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,SAAU,SACV,QAAAD,CACD,EACD,OAAuBxB,EAAa,QAAQ,cAC1C,MACA,CACE,IAAK,KAAK,MAAM,IAChB,IAAK,KAAK,IACV,MAAAyB,CACR,CACK,CACL,CACA,CACA7B,EAAcE,EAAO,cAAe,OAAO,EAC3CF,EAAcE,EAAO,UAAWM,EAAgB,QAAQ,KAAK,EAC7DR,EAAcE,EAAO,YAAa,EAAI", "x_google_ignoreList": [0]}