{"version": 3, "file": "Facebook-BgUvDFqF.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/Facebook.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Facebook_exports = {};\n__export(Facebook_exports, {\n  default: () => Facebook\n});\nmodule.exports = __toCommonJS(Facebook_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://connect.facebook.net/en_US/sdk.js\";\nconst SDK_GLOBAL = \"FB\";\nconst SDK_GLOBAL_READY = \"fbAsyncInit\";\nconst PLAYER_ID_PREFIX = \"facebook-player-\";\nclass Facebook extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"mute\");\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"unmute\");\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    if (isReady) {\n      (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((FB) => FB.XFBML.parse());\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL, SDK_GLOBAL_READY).then((FB) => {\n      FB.init({\n        appId: this.props.config.appId,\n        xfbml: true,\n        version: this.props.config.version\n      });\n      FB.Event.subscribe(\"xfbml.render\", (msg) => {\n        this.props.onLoaded();\n      });\n      FB.Event.subscribe(\"xfbml.ready\", (msg) => {\n        if (msg.type === \"video\" && msg.id === this.playerID) {\n          this.player = msg.instance;\n          this.player.subscribe(\"startedPlaying\", this.props.onPlay);\n          this.player.subscribe(\"paused\", this.props.onPause);\n          this.player.subscribe(\"finishedPlaying\", this.props.onEnded);\n          this.player.subscribe(\"startedBuffering\", this.props.onBuffer);\n          this.player.subscribe(\"finishedBuffering\", this.props.onBufferEnd);\n          this.player.subscribe(\"error\", this.props.onError);\n          if (this.props.muted) {\n            this.callPlayer(\"mute\");\n          } else {\n            this.callPlayer(\"unmute\");\n          }\n          this.props.onReady();\n          document.getElementById(this.playerID).querySelector(\"iframe\").style.visibility = \"visible\";\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentPosition\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const { attributes } = this.props.config;\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style,\n        id: this.playerID,\n        className: \"fb-video\",\n        \"data-href\": this.props.url,\n        \"data-autoplay\": this.props.playing ? \"true\" : \"false\",\n        \"data-allowfullscreen\": \"true\",\n        \"data-controls\": this.props.controls ? \"true\" : \"false\",\n        ...attributes\n      }\n    );\n  }\n}\n__publicField(Facebook, \"displayName\", \"Facebook\");\n__publicField(Facebook, \"canPlay\", import_patterns.canPlay.facebook);\n__publicField(Facebook, \"loopOnEnded\", true);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "Facebook_exports", "Facebook", "Facebook_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "SDK_GLOBAL_READY", "PLAYER_ID_PREFIX", "url", "isReady", "FB", "msg", "seconds", "keepPlaying", "fraction", "attributes", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAmB,CAAE,EACzBd,EAASc,EAAkB,CACzB,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAgB,EAC1CG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,4CACVC,EAAa,KACbC,EAAmB,cACnBC,EAAmB,mBACzB,MAAMX,UAAiBE,EAAa,SAAU,CAC5C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,WAAY,KAAK,MAAM,OAAO,UAAY,GAAGa,CAAgB,MAAOP,EAAa,cAAY,CAAG,EAAE,EACtHN,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,WAAW,MAAM,CAC5B,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAClC,KAAK,WAAW,QAAQ,CAC9B,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKc,EAAKC,EAAS,CACjB,GAAIA,EAAS,IACPT,EAAa,QAAQI,EAASC,EAAYC,CAAgB,EAAE,KAAMI,GAAOA,EAAG,MAAM,MAAK,CAAE,EAC7F,MACN,IACQV,EAAa,QAAQI,EAASC,EAAYC,CAAgB,EAAE,KAAMI,GAAO,CAC3EA,EAAG,KAAK,CACN,MAAO,KAAK,MAAM,OAAO,MACzB,MAAO,GACP,QAAS,KAAK,MAAM,OAAO,OACnC,CAAO,EACDA,EAAG,MAAM,UAAU,eAAiBC,GAAQ,CAC1C,KAAK,MAAM,SAAU,CAC7B,CAAO,EACDD,EAAG,MAAM,UAAU,cAAgBC,GAAQ,CACrCA,EAAI,OAAS,SAAWA,EAAI,KAAO,KAAK,WAC1C,KAAK,OAASA,EAAI,SAClB,KAAK,OAAO,UAAU,iBAAkB,KAAK,MAAM,MAAM,EACzD,KAAK,OAAO,UAAU,SAAU,KAAK,MAAM,OAAO,EAClD,KAAK,OAAO,UAAU,kBAAmB,KAAK,MAAM,OAAO,EAC3D,KAAK,OAAO,UAAU,mBAAoB,KAAK,MAAM,QAAQ,EAC7D,KAAK,OAAO,UAAU,oBAAqB,KAAK,MAAM,WAAW,EACjE,KAAK,OAAO,UAAU,QAAS,KAAK,MAAM,OAAO,EAC7C,KAAK,MAAM,MACb,KAAK,WAAW,MAAM,EAEtB,KAAK,WAAW,QAAQ,EAE1B,KAAK,MAAM,QAAS,EACpB,SAAS,eAAe,KAAK,QAAQ,EAAE,cAAc,QAAQ,EAAE,MAAM,WAAa,UAE5F,CAAO,CACP,CAAK,CACL,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACT,CACE,OAAOC,EAASC,EAAc,GAAM,CAClC,KAAK,WAAW,OAAQD,CAAO,EAC1BC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,CAAQ,CACzC,CACE,aAAc,CACZ,OAAO,KAAK,WAAW,aAAa,CACxC,CACE,gBAAiB,CACf,OAAO,KAAK,WAAW,oBAAoB,CAC/C,CACE,kBAAmB,CACjB,OAAO,IACX,CACE,QAAS,CACP,KAAM,CAAE,WAAAC,CAAU,EAAK,KAAK,MAAM,OAC5BC,EAAQ,CACZ,MAAO,OACP,OAAQ,MACT,EACD,OAAuBlB,EAAa,QAAQ,cAC1C,MACA,CACE,MAAAkB,EACA,GAAI,KAAK,SACT,UAAW,WACX,YAAa,KAAK,MAAM,IACxB,gBAAiB,KAAK,MAAM,QAAU,OAAS,QAC/C,uBAAwB,OACxB,gBAAiB,KAAK,MAAM,SAAW,OAAS,QAChD,GAAGD,CACX,CACK,CACL,CACA,CACArB,EAAcE,EAAU,cAAe,UAAU,EACjDF,EAAcE,EAAU,UAAWM,EAAgB,QAAQ,QAAQ,EACnER,EAAcE,EAAU,cAAe,EAAI", "x_google_ignoreList": [0]}