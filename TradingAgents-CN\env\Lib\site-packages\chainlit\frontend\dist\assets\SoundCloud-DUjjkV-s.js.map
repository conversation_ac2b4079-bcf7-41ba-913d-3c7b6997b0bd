{"version": 3, "file": "SoundCloud-DUjjkV-s.js", "sources": ["../../node_modules/.pnpm/react-player@2.16.0_react@18.3.1/node_modules/react-player/lib/players/SoundCloud.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar SoundCloud_exports = {};\n__export(SoundCloud_exports, {\n  default: () => SoundCloud\n});\nmodule.exports = __toCommonJS(SoundCloud_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://w.soundcloud.com/player/api.js\";\nconst SDK_GLOBAL = \"SC\";\nclass SoundCloud extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"fractionLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setVolume(0);\n    });\n    __publicField(this, \"unmute\", () => {\n      if (this.props.volume !== null) {\n        this.setVolume(this.props.volume);\n      }\n    });\n    __publicField(this, \"ref\", (iframe) => {\n      this.iframe = iframe;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((SC) => {\n      if (!this.iframe)\n        return;\n      const { PLAY, PLAY_PROGRESS, PAUSE, FINISH, ERROR } = SC.Widget.Events;\n      if (!isReady) {\n        this.player = SC.Widget(this.iframe);\n        this.player.bind(PLAY, this.props.onPlay);\n        this.player.bind(PAUSE, () => {\n          const remaining = this.duration - this.currentTime;\n          if (remaining < 0.05) {\n            return;\n          }\n          this.props.onPause();\n        });\n        this.player.bind(PLAY_PROGRESS, (e) => {\n          this.currentTime = e.currentPosition / 1e3;\n          this.fractionLoaded = e.loadedProgress;\n        });\n        this.player.bind(FINISH, () => this.props.onEnded());\n        this.player.bind(ERROR, (e) => this.props.onError(e));\n      }\n      this.player.load(url, {\n        ...this.props.config.options,\n        callback: () => {\n          this.player.getDuration((duration) => {\n            this.duration = duration / 1e3;\n            this.props.onReady();\n          });\n        }\n      });\n    });\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seekTo\", seconds * 1e3);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction * 100);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.fractionLoaded * this.duration;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"iframe\",\n      {\n        ref: this.ref,\n        src: `https://w.soundcloud.com/player/?url=${encodeURIComponent(this.props.url)}`,\n        style,\n        frameBorder: 0,\n        allow: \"autoplay\"\n      }\n    );\n  }\n}\n__publicField(SoundCloud, \"displayName\", \"SoundCloud\");\n__publicField(SoundCloud, \"canPlay\", import_patterns.canPlay.soundcloud);\n__publicField(SoundCloud, \"loopOnEnded\", true);\n"], "names": ["__create", "__defProp", "__getOwnPropDesc", "__getOwnPropNames", "__getProtoOf", "__hasOwnProp", "__defNormalProp", "obj", "key", "value", "__export", "target", "all", "name", "__copyProps", "to", "from", "except", "desc", "__toESM", "mod", "isNodeMode", "__toCommonJS", "__publicField", "SoundCloud_exports", "SoundCloud", "SoundCloud_1", "import_react", "require$$0", "import_utils", "require$$1", "import_patterns", "require$$2", "SDK_URL", "SDK_GLOBAL", "iframe", "url", "isReady", "SC", "PLAY", "PLAY_PROGRESS", "PAUSE", "FINISH", "ERROR", "e", "duration", "seconds", "keepPlaying", "fraction", "display", "style"], "mappings": "mZAAA,IAAIA,EAAW,OAAO,OAClBC,EAAY,OAAO,eACnBC,EAAmB,OAAO,yBAC1BC,EAAoB,OAAO,oBAC3BC,EAAe,OAAO,eACtBC,EAAe,OAAO,UAAU,eAChCC,EAAkB,CAACC,EAAKC,EAAKC,IAAUD,KAAOD,EAAMN,EAAUM,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAC,CAAK,CAAE,EAAIF,EAAIC,CAAG,EAAIC,EACtJC,EAAW,CAACC,EAAQC,IAAQ,CAC9B,QAASC,KAAQD,EACfX,EAAUU,EAAQE,EAAM,CAAE,IAAKD,EAAIC,CAAI,EAAG,WAAY,GAAM,CAChE,EACIC,EAAc,CAACC,EAAIC,EAAMC,EAAQC,IAAS,CAC5C,GAAIF,GAAQ,OAAOA,GAAS,UAAY,OAAOA,GAAS,WACtD,QAASR,KAAOL,EAAkBa,CAAI,EAChC,CAACX,EAAa,KAAKU,EAAIP,CAAG,GAAKA,IAAQS,GACzChB,EAAUc,EAAIP,EAAK,CAAE,IAAK,IAAMQ,EAAKR,CAAG,EAAG,WAAY,EAAEU,EAAOhB,EAAiBc,EAAMR,CAAG,IAAMU,EAAK,WAAY,EAEvH,OAAOH,CACT,EACII,EAAU,CAACC,EAAKC,EAAYV,KAAYA,EAASS,GAAO,KAAOpB,EAASI,EAAagB,CAAG,CAAC,EAAI,CAAE,EAAEN,EAKrF,CAACM,GAAO,CAACA,EAAI,WAAanB,EAAUU,EAAQ,UAAW,CAAE,MAAOS,EAAK,WAAY,EAAI,CAAE,EAAIT,EACzGS,CACF,GACIE,EAAgBF,GAAQN,EAAYb,EAAU,CAAA,EAAI,aAAc,CAAE,MAAO,EAAM,CAAA,EAAGmB,CAAG,EACrFG,EAAgB,CAAChB,EAAKC,EAAKC,KAC7BH,EAAgBC,EAAK,OAAOC,GAAQ,SAAWA,EAAM,GAAKA,EAAKC,CAAK,EAC7DA,GAELe,EAAqB,CAAE,EAC3Bd,EAASc,EAAoB,CAC3B,QAAS,IAAMC,CACjB,CAAC,EACD,IAAAC,EAAiBJ,EAAaE,CAAkB,EAC5CG,EAAeR,EAAQS,CAAgB,EACvCC,EAAeC,EACfC,EAAkBC,EACtB,MAAMC,EAAU,yCACVC,EAAa,KACnB,MAAMT,UAAmBE,EAAa,SAAU,CAC9C,aAAc,CACZ,MAAM,GAAG,SAAS,EAClBJ,EAAc,KAAM,aAAcM,EAAa,UAAU,EACzDN,EAAc,KAAM,WAAY,IAAI,EACpCA,EAAc,KAAM,cAAe,IAAI,EACvCA,EAAc,KAAM,iBAAkB,IAAI,EAC1CA,EAAc,KAAM,OAAQ,IAAM,CAChC,KAAK,UAAU,CAAC,CACtB,CAAK,EACDA,EAAc,KAAM,SAAU,IAAM,CAC9B,KAAK,MAAM,SAAW,MACxB,KAAK,UAAU,KAAK,MAAM,MAAM,CAExC,CAAK,EACDA,EAAc,KAAM,MAAQY,GAAW,CACrC,KAAK,OAASA,CACpB,CAAK,CACL,CACE,mBAAoB,CAClB,KAAK,MAAM,SAAW,KAAK,MAAM,QAAQ,IAAI,CACjD,CACE,KAAKC,EAAKC,EAAS,IACbR,EAAa,QAAQI,EAASC,CAAU,EAAE,KAAMI,GAAO,CACzD,GAAI,CAAC,KAAK,OACR,OACF,KAAM,CAAE,KAAAC,EAAM,cAAAC,EAAe,MAAAC,EAAO,OAAAC,EAAQ,MAAAC,CAAO,EAAGL,EAAG,OAAO,OAC3DD,IACH,KAAK,OAASC,EAAG,OAAO,KAAK,MAAM,EACnC,KAAK,OAAO,KAAKC,EAAM,KAAK,MAAM,MAAM,EACxC,KAAK,OAAO,KAAKE,EAAO,IAAM,CACV,KAAK,SAAW,KAAK,YACvB,KAGhB,KAAK,MAAM,QAAS,CAC9B,CAAS,EACD,KAAK,OAAO,KAAKD,EAAgBI,GAAM,CACrC,KAAK,YAAcA,EAAE,gBAAkB,IACvC,KAAK,eAAiBA,EAAE,cAClC,CAAS,EACD,KAAK,OAAO,KAAKF,EAAQ,IAAM,KAAK,MAAM,SAAS,EACnD,KAAK,OAAO,KAAKC,EAAQC,GAAM,KAAK,MAAM,QAAQA,CAAC,CAAC,GAEtD,KAAK,OAAO,KAAKR,EAAK,CACpB,GAAG,KAAK,MAAM,OAAO,QACrB,SAAU,IAAM,CACd,KAAK,OAAO,YAAaS,GAAa,CACpC,KAAK,SAAWA,EAAW,IAC3B,KAAK,MAAM,QAAS,CAChC,CAAW,CACX,CACA,CAAO,CACP,CAAK,CACL,CACE,MAAO,CACL,KAAK,WAAW,MAAM,CAC1B,CACE,OAAQ,CACN,KAAK,WAAW,OAAO,CAC3B,CACE,MAAO,CACT,CACE,OAAOC,EAASC,EAAc,GAAM,CAClC,KAAK,WAAW,SAAUD,EAAU,GAAG,EAClCC,GACH,KAAK,MAAO,CAElB,CACE,UAAUC,EAAU,CAClB,KAAK,WAAW,YAAaA,EAAW,GAAG,CAC/C,CACE,aAAc,CACZ,OAAO,KAAK,QAChB,CACE,gBAAiB,CACf,OAAO,KAAK,WAChB,CACE,kBAAmB,CACjB,OAAO,KAAK,eAAiB,KAAK,QACtC,CACE,QAAS,CACP,KAAM,CAAE,QAAAC,GAAY,KAAK,MACnBC,EAAQ,CACZ,MAAO,OACP,OAAQ,OACR,QAAAD,CACD,EACD,OAAuBtB,EAAa,QAAQ,cAC1C,SACA,CACE,IAAK,KAAK,IACV,IAAK,wCAAwC,mBAAmB,KAAK,MAAM,GAAG,CAAC,GAC/E,MAAAuB,EACA,YAAa,EACb,MAAO,UACf,CACK,CACL,CACA,CACA3B,EAAcE,EAAY,cAAe,YAAY,EACrDF,EAAcE,EAAY,UAAWM,EAAgB,QAAQ,UAAU,EACvER,EAAcE,EAAY,cAAe,EAAI", "x_google_ignoreList": [0]}